<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Calendar - Tutor Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .time-slot {
            height: 60px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
        }
        .time-slot:hover {
            background-color: #f3f4f6;
        }
        .time-slot.available {
            background-color: #dcfce7;
            border-color: #16a34a;
        }
        .time-slot.booked {
            background-color: #fef3c7;
            border-color: #d97706;
        }
        .time-slot.unavailable {
            background-color: #fee2e2;
            border-color: #dc2626;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
            min-height: 100%;
        }
        .calendar-scroll-container {
            max-height: 70vh;
            overflow-y: auto;
            overflow-x: hidden;
        }
        .day-header {
            background-color: #f9fafb;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            border: 1px solid #e5e7eb;
        }
        .time-label {
            background-color: #f9fafb;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <a href="#" class="text-2xl font-bold text-indigo-600">IndianTutors</a>
                    <button onclick="goBackToTutorDashboard()" class="text-gray-600 hover:text-indigo-600 transition-colors">
                        ← Back to Dashboard
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userEmail" class="text-gray-600">Loading...</span>
                    <button onclick="handleLogout()" class="text-red-600 hover:text-red-700">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Loading State -->
    <div id="loadingState" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p class="text-gray-600 mt-4">Loading calendar...</p>
        </div>
    </div>

    <!-- Error State -->
    <div id="errorState" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 hidden">
        <div class="text-center">
            <div class="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 class="text-xl font-semibold text-gray-900 mb-2">Failed to Load Calendar</h2>
            <p class="text-gray-600 mb-4">Please check your connection and try again.</p>
            <button onclick="window.location.reload()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                Retry
            </button>
        </div>
    </div>

    <!-- Calendar Container -->
    <div id="calendarContainer" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 hidden">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Calendar</h1>
                <p class="text-gray-600">Manage your availability and view upcoming lessons</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="saveAvailability()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 font-medium">
                    Save Availability
                </button>
                <button onclick="resetAvailability()" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 font-medium">
                    Reset
                </button>
            </div>
        </div>

        <!-- Legend -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
            <h3 class="text-sm font-semibold text-gray-900 mb-3">Legend</h3>
            <div class="flex flex-wrap gap-6">
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-green-200 border border-green-500 rounded"></div>
                    <span class="text-sm text-gray-600">Available</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-yellow-200 border border-yellow-600 rounded"></div>
                    <span class="text-sm text-gray-600">Booked</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-red-200 border border-red-600 rounded"></div>
                    <span class="text-sm text-gray-600">Unavailable</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-white border border-gray-300 rounded"></div>
                    <span class="text-sm text-gray-600">Not Set</span>
                </div>
            </div>
        </div>

        <!-- Week Navigation -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div class="flex justify-between items-center">
                <button onclick="previousWeek()" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    <span>Previous</span>
                </button>
                <div class="text-center">
                    <h2 id="weekRange" class="text-lg font-semibold text-gray-900">Loading...</h2>
                    <p class="text-sm text-gray-500">Click time slots to set availability</p>
                </div>
                <button onclick="nextWeek()" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                    <span>Next</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="calendar-scroll-container">
                <div id="calendarGrid" class="calendar-grid">
                    <!-- Calendar will be generated here -->
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-semibold text-blue-900 mb-2">How to use:</h3>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• Click on time slots to toggle your availability</li>
                <li>• Green slots = Available for booking</li>
                <li>• Yellow slots = Already booked by students</li>
                <li>• Red slots = Unavailable</li>
                <li>• Click "Save Availability" to update your schedule</li>
            </ul>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Calendar JavaScript -->
    <script src="tutor-calendar.js?v=7"></script>
</body>
</html>
