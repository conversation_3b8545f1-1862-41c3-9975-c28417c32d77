<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - Enhanced Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .message-bubble {
            max-width: 70%;
            word-wrap: break-word;
        }
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f3f4f6;
            border-radius: 18px;
            margin: 4px 0;
        }
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        .typing-dot {
            width: 6px;
            height: 6px;
            background: #6b7280;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        .file-preview {
            max-width: 200px;
            border-radius: 8px;
        }
        .reaction-picker {
            position: absolute;
            bottom: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }
        .message-status {
            font-size: 12px;
            margin-left: 4px;
        }
        .unread-badge {
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-xl font-bold text-indigo-600">MyTutor</a>
                    <span class="ml-4 text-gray-600">Enhanced Messages</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="userInfo" class="text-sm text-gray-600"></div>
                    <button onclick="logout()" class="text-sm text-red-600 hover:text-red-800">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="bg-white rounded-lg shadow-sm h-[calc(100vh-200px)] flex">
            <!-- Chat List Sidebar -->
            <div class="w-1/3 border-r border-gray-200 flex flex-col">
                <!-- Search Bar -->
                <div class="p-4 border-b border-gray-200">
                    <div class="relative">
                        <input type="text" id="chatSearch" placeholder="Search conversations..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Chat List -->
                <div id="chatsList" class="flex-1 overflow-y-auto">
                    <div class="p-4 text-center text-gray-500">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto mb-2"></div>
                        Loading conversations...
                    </div>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="flex-1 flex flex-col">
                <!-- Chat Header -->
                <div id="chatHeader" class="p-4 border-b border-gray-200 bg-gray-50 hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <img id="otherUserAvatar" src="" alt="" class="w-10 h-10 rounded-full">
                            <div>
                                <h3 id="otherUserName" class="font-semibold text-gray-900"></h3>
                                <p id="otherUserStatus" class="text-sm text-gray-500">Online</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </button>
                            <button class="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Messages Area -->
                <div id="messagesArea" class="flex-1 overflow-y-auto p-4 space-y-4">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <p class="text-lg font-medium">Select a conversation</p>
                        <p class="text-sm">Choose a chat from the sidebar to start messaging</p>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div id="typingIndicator" class="px-4 hidden">
                    <div class="typing-indicator">
                        <span class="text-sm text-gray-500 mr-2">Someone is typing</span>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>

                <!-- Message Input -->
                <div id="messageInputArea" class="p-4 border-t border-gray-200 hidden">
                    <div class="message-input-container flex items-end space-x-2">
                        <!-- File Upload Button -->
                        <button type="button" class="file-btn px-3 py-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100">
                            📎
                        </button>
                        
                        <!-- Emoji Button -->
                        <button type="button" class="emoji-btn px-3 py-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100">
                            😊
                        </button>
                        
                        <!-- Message Input -->
                        <div class="flex-1 relative">
                            <textarea id="messageText" 
                                    placeholder="Type your message..." 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                                    rows="1"></textarea>
                        </div>
                        
                        <!-- Send Button -->
                        <button onclick="sendMessage()" 
                                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="simple-messaging.js?v=1"></script>
    <script src="enhanced-messaging.js?v=1"></script>
    <script src="messages-enhanced.js?v=1"></script>
</body>
</html>
