<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Test Data</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Insert Test Data</h1>
        <div id="status" class="mb-4"></div>
        <button id="insertBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
            Insert Test Tutors
        </button>
        <div id="results" class="mt-6"></div>
    </div>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        const testTutors = [
            {
                name: 'Dan Chen',
                language: 'Chinese (Mandarin)',
                photo_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                country: 'China',
                country_flag: '🇨🇳',
                native_language: 'Chinese (Mandarin)',
                languages_spoken: [
                    {"language": "Chinese (Mandarin)", "proficiency": "Native"},
                    {"language": "English", "proficiency": "Fluent"},
                    {"language": "Korean", "proficiency": "Intermediate"}
                ],
                bio: 'Beginner - Advanced, Conversation, HSK, Business Chinese, The Writer of www.thechairmansbao.com',
                bio_headline: 'Professional Chinese Teacher',
                rate: 12.00,
                rating: 5.0,
                total_students: 723,
                total_lessons: 9481,
                video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                teaching_style: 'I focus on practical communication skills and tailor my lessons to each student\'s needs and interests. My classes are always efficient, as I focus on the key points without wasting any time.',
                resume: 'Bachelor\'s degree in Chinese Literature. Certified HSK instructor with 8+ years of experience. Writer for The Chairman\'s Bao.',
                about_me: 'I have many interests and hobbies, such as playing basketball, playing ping-pong, traveling, watching NBA. I am a super fan of NBA. I went to America in March 2017, and stayed there for half a year. During this half a year, I visited many places in America, like Texas, Utah, Los Angeles, Las Vegas, Yellow Stone National Park, Grand Canyon. I like to talk about many different topics, such as travel, sports, movies, music, food, culture, business, etc.',
                me_as_teacher: 'I have been teaching Chinese for over 8 years. I am patient, friendly, and passionate about helping students achieve their language learning goals. I specialize in HSK preparation and business Chinese.',
                tags: ["Business Chinese", "HSK Preparation", "Conversational", "Grammar", "Beginner Friendly"],
                is_professional: true,
                is_active: true
            },
            {
                name: 'Priya Sharma',
                language: 'Hindi',
                photo_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
                country: 'India',
                country_flag: '🇮🇳',
                native_language: 'Hindi',
                languages_spoken: [
                    {"language": "Hindi", "proficiency": "Native"},
                    {"language": "English", "proficiency": "Fluent"},
                    {"language": "Punjabi", "proficiency": "Conversational"}
                ],
                bio: 'Experienced Hindi teacher specializing in conversational Hindi and grammar.',
                bio_headline: 'Native Hindi Speaker & Teacher',
                rate: 8.00,
                rating: 4.8,
                total_students: 156,
                total_lessons: 2340,
                video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                teaching_style: 'Interactive and fun approach to learning Hindi with focus on practical conversation.',
                resume: 'Master\'s in Hindi Literature. 5+ years teaching experience.',
                about_me: 'I love teaching Hindi and sharing Indian culture with students from around the world.',
                me_as_teacher: 'I am patient and encouraging, helping students build confidence in speaking Hindi.',
                tags: ["Conversational", "Grammar", "Beginner Friendly", "Cultural Context"],
                is_professional: false,
                is_active: true
            }
        ];

        document.getElementById('insertBtn').addEventListener('click', async () => {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.innerHTML = '<div class="bg-blue-100 text-blue-800 p-4 rounded">Inserting test data...</div>';
            
            try {
                // First, check if tutors already exist
                const { data: existingTutors, error: checkError } = await supabase
                    .from('tutors')
                    .select('name');
                
                if (checkError) {
                    throw checkError;
                }
                
                statusDiv.innerHTML = `<div class="bg-yellow-100 text-yellow-800 p-4 rounded">Found ${existingTutors.length} existing tutors. Inserting new ones...</div>`;
                
                // Insert test tutors
                const { data, error } = await supabase
                    .from('tutors')
                    .insert(testTutors)
                    .select();
                
                if (error) {
                    throw error;
                }
                
                statusDiv.innerHTML = '<div class="bg-green-100 text-green-800 p-4 rounded">✅ Test data inserted successfully!</div>';
                resultsDiv.innerHTML = `
                    <h2 class="text-xl font-bold mb-4">Inserted Tutors:</h2>
                    <div class="space-y-4">
                        ${data.map(tutor => `
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h3 class="font-bold">${tutor.name}</h3>
                                <p>ID: ${tutor.id}</p>
                                <p>Language: ${tutor.language}</p>
                                <a href="profile.html?id=${tutor.id}" target="_blank" class="text-blue-600 hover:underline">
                                    View Profile →
                                </a>
                            </div>
                        `).join('')}
                    </div>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                statusDiv.innerHTML = `<div class="bg-red-100 text-red-800 p-4 rounded">❌ Error: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
