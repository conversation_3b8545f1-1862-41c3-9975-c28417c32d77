<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indian Language Tutors - Learn with Native Speakers</title>
    <!-- GitHub Pages Deployment Fix -->
    <meta name="description" content="Learn Hindi, Tamil, Bengali, Telugu and other Indian languages with certified native tutors. 1-on-1 video lessons with flexible schedules.">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Google Sign-In (handled by Supabase) -->

    <!-- Custom Styles -->
    <link rel="stylesheet" href="responsive-global.css?v=12&t=20250614-critical-fix">
    <link rel="stylesheet" href="styles.css?v=12&t=20250614-critical-fix">
</head>
<body>
    <!-- Navigation -->
    <nav class="responsive-nav navbar">
        <div class="responsive-container nav-container">
            <div class="nav-content">
                <div class="nav-left">
                    <a href="#" class="nav-logo">IndianTutors</a>
                </div>
                <div class="nav-right nav-links">
                    <a href="#teachers" class="nav-link">Find a Teacher</a>
                    <button class="btn btn-primary nav-btn login-btn" onclick="openLoginModal()">Log in</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="responsive-container hero-container">
            <div class="hero-content">
                <h1 class="hero-title">Become fluent in any Indian language</h1>
                <ul class="hero-features">
                    <li>
                        <span class="feature-icon">📹</span>
                        1-on-1 video lessons
                    </li>
                    <li>
                        <span class="feature-icon">🎓</span>
                        Certified tutors
                    </li>
                    <li>
                        <span class="feature-icon">⏰</span>
                        Flexible schedules and prices
                    </li>
                </ul>
                <button class="btn btn-primary cta-button btn-full-mobile" onclick="openLoginModal()">
                    <span class="google-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                    </span>
                    Start Now with Google
                </button>
            </div>
            <div class="hero-image">
                <div class="hero-illustration">
                    <div class="illustration-card">
                        <div class="card-header">
                            <div class="avatar"></div>
                            <div class="tutor-info">
                                <div class="tutor-name"></div>
                                <div class="tutor-lang"></div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="lesson-preview"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Languages Section -->
    <section class="languages">
        <div class="responsive-container languages-container">
            <h2 class="languages-title">Popular Indian Languages</h2>
            <div class="responsive-grid grid-4 grid-mobile-2 languages-grid">
                <div class="language-pill">
                    <span class="language-name">Hindi</span>
                    <span class="tutor-count">1,247 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Tamil</span>
                    <span class="tutor-count">892 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Bengali</span>
                    <span class="tutor-count">634 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Telugu</span>
                    <span class="tutor-count">578 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Marathi</span>
                    <span class="tutor-count">423 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Gujarati</span>
                    <span class="tutor-count">356 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Kannada</span>
                    <span class="tutor-count">289 tutors</span>
                </div>
                <div class="language-pill">
                    <span class="language-name">Malayalam</span>
                    <span class="tutor-count">234 tutors</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="loginModal" class="modal-overlay" onclick="closeLoginModal(event)">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h2 class="modal-title">Welcome to IndianTutors</h2>
                <button class="modal-close" onclick="closeLoginModal()" aria-label="Close modal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <div class="modal-body">
                <p class="modal-subtitle">Start your language learning journey today</p>

                <button class="btn btn-primary google-login-btn btn-full-mobile" onclick="handleGoogleLogin()">
                    <div class="google-icon-wrapper">
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                    </div>
                    <span>Continue with Google</span>
                </button>


                <div class="modal-divider">
                    <span>or</span>
                </div>

                <div class="alternative-options">
                    <p class="coming-soon">Email login coming soon</p>
                </div>
            </div>

            <div class="modal-footer">
                <p class="terms-text">
                    By continuing, you agree to our
                    <a href="#" class="terms-link">Terms of Service</a> and
                    <a href="#" class="terms-link">Privacy Policy</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Authentication handled by Supabase -->

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Authentication Script -->
    <script>
        // Supabase Configuration
        const SUPABASE_URL = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';

        let supabase = null;

        // Initialize Supabase when page loads
        window.addEventListener('load', function() {
            initializeSupabase();
        });

        function initializeSupabase() {
            if (typeof window.supabase !== 'undefined') {
                if (SUPABASE_URL === 'YOUR_SUPABASE_URL_HERE' || SUPABASE_ANON_KEY === 'YOUR_SUPABASE_ANON_KEY_HERE') {
                    console.error('Supabase credentials not configured');
                    showErrorMessage('Authentication not configured. Please set up Supabase credentials.');
                    return;
                }

                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                console.log('Supabase initialized successfully');

                // Check if user is already logged in
                checkAuthState();
            } else {
                console.error('Supabase library not loaded');
                setTimeout(initializeSupabase, 1000); // Retry after 1 second
            }
        }

        async function checkAuthState() {
            if (!supabase) return;

            const { data: { session } } = await supabase.auth.getSession();
            if (session) {
                console.log('User already logged in:', session.user);
                // Redirect to React app if already authenticated
                window.location.href = '/my-tutor-app/react-version/?redirect=dashboard';
            }
        }

        async function handleGoogleLogin() {
            console.log('Initiating Supabase Google login...');

            if (!supabase) {
                showErrorMessage('Authentication service not available. Please refresh the page.');
                return;
            }

            try {
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: `${window.location.origin}/my-tutor-app/react-version/?redirect=dashboard`
                    }
                });

                if (error) {
                    console.error('Supabase auth error:', error);
                    showErrorMessage('Login failed: ' + error.message);
                } else {
                    console.log('Google login initiated successfully');
                    closeLoginModal();
                    showSuccessMessage('Redirecting to Google...');
                }
            } catch (error) {
                console.error('Login error:', error);
                showErrorMessage('Login failed. Please try again.');
            }
        }

        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #38B000;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: Inter, sans-serif;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            successDiv.textContent = message;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 3000);
        }

        function showErrorMessage(message) {
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #DC2626;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: Inter, sans-serif;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);

            setTimeout(() => {
                if (document.body.contains(errorDiv)) {
                    document.body.removeChild(errorDiv);
                }
            }, 5000);
        }

        // Modal functions
        function openLoginModal() {
            console.log('Opening modal...');
            const modal = document.getElementById('loginModal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                console.log('Modal opened successfully');
            } else {
                console.error('Modal element not found');
            }
        }

        function closeLoginModal(event) {
            console.log('Closing modal...');
            if (event && event.target !== event.currentTarget) {
                return;
            }

            const modal = document.getElementById('loginModal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
                console.log('Modal closed successfully');
            }
        }



        // Close modal on Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeLoginModal();
            }
        });

        // Make functions globally available
        window.openLoginModal = openLoginModal;
        window.closeLoginModal = closeLoginModal;
        window.handleGoogleLogin = handleGoogleLogin;

        console.log('Authentication system loaded successfully');
    </script>
</body>
</html>