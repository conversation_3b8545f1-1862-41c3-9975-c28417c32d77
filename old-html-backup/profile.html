<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutor Profile - IndianTutors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tab-button.active {
            border-bottom: 2px solid #00C2B3;
            color: #00C2B3;
            font-weight: 600;
        }
        .availability-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .availability-cell {
            background-color: white;
            padding: 8px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }
        .availability-cell.header {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        .availability-cell.time-label {
            background-color: #f9fafb;
            font-weight: 500;
            color: #6b7280;
        }
        .availability-cell.available {
            background-color: #dcfce7;
            color: #166534;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }
        .availability-cell.available:hover {
            background-color: #bbf7d0;
            transform: scale(1.05);
        }
        .availability-cell.unavailable {
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9) translateY(20px);
            transition: all 0.3s ease;
        }
        .modal-overlay.active .modal-content {
            transform: scale(1) translateY(0);
        }

        /* Detailed Calendar Styles */
        .detailed-calendar {
            display: grid;
            grid-template-columns: 100px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .calendar-cell {
            background-color: white;
            padding: 6px;
            min-height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            transition: all 0.2s;
        }
        .calendar-cell.header {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }
        .calendar-cell.time-label {
            background-color: #f9fafb;
            font-weight: 500;
            color: #6b7280;
            font-size: 0.75rem;
        }
        .calendar-cell.available {
            background-color: #dcfce7;
            color: #166534;
            cursor: pointer;
        }
        .calendar-cell.available:hover {
            background-color: #bbf7d0;
            transform: scale(1.1);
        }
        .calendar-cell.selected {
            background-color: #00C2B3;
            color: white;
        }
        .calendar-cell.unavailable {
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .availability-grid {
                grid-template-columns: 60px repeat(7, 1fr);
                gap: 0.5px;
            }
            .availability-cell {
                padding: 4px;
                min-height: 30px;
                font-size: 0.75rem;
            }
            .detailed-calendar {
                grid-template-columns: 70px repeat(7, 1fr);
                gap: 0.5px;
            }
            .calendar-cell {
                padding: 3px;
                min-height: 25px;
                font-size: 0.625rem;
            }
            .modal-content {
                max-width: 95vw;
                margin: 1rem;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .availability-grid {
                grid-template-columns: 50px repeat(7, 1fr);
            }
            .availability-cell {
                padding: 2px;
                min-height: 25px;
                font-size: 0.625rem;
            }
            .detailed-calendar {
                grid-template-columns: 60px repeat(7, 1fr);
            }
            .calendar-cell {
                padding: 2px;
                min-height: 20px;
                font-size: 0.5rem;
            }
        }
        .sticky-sidebar {
            position: sticky;
            top: 2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }
        .interest-tag {
            display: inline-block;
            background-color: #f3f4f6;
            color: #374151;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            margin: 0.125rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="home.html" class="text-2xl font-bold" style="color: #00C2B3;">IndianTutors</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="findteacher.html" class="text-gray-600 hover:text-gray-900 flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <span>Back to Tutors</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Loading State -->
    <div id="loadingState" class="flex items-center justify-center min-h-screen">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto" style="border-color: #00C2B3;"></div>
            <p class="text-gray-600 mt-4">Loading tutor profile...</p>
        </div>
    </div>

    <!-- Error State -->
    <div id="errorState" class="hidden flex items-center justify-center min-h-screen">
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Tutor Not Found</h2>
            <p class="text-gray-600 mb-4">The tutor profile you're looking for doesn't exist.</p>
            <a href="findteacher.html" class="text-white px-4 py-2 rounded-lg transition-colors" style="background-color: #00C2B3;" onmouseover="this.style.backgroundColor='#00A89B'" onmouseout="this.style.backgroundColor='#00C2B3'">
                Browse Tutors
            </a>
        </div>
    </div>

    <!-- Profile Content -->
    <div id="profileContent" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Section (Main Content) -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Tutor Header -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-start space-x-6 mb-6">
                        <div class="relative">
                            <img id="tutorAvatar" src="" alt="Tutor" class="w-24 h-24 rounded-full object-cover border-2 border-gray-100">
                            <div id="onlineStatus" class="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h1 id="tutorName" class="text-3xl font-bold text-gray-900"></h1>
                                <span id="tutorFlag" class="text-2xl"></span>
                                <span id="professionalBadge" class="hidden text-white text-xs px-3 py-1 rounded-full font-medium" style="background-color: #00C2B3;">PROFESSIONAL TEACHER</span>
                            </div>

                            <!-- Languages Taught -->
                            <div class="mb-3">
                                <span class="text-sm text-gray-500">Teaches</span>
                                <div id="teachesLanguages" class="flex flex-wrap gap-2 mt-1">
                                    <!-- Will be populated -->
                                </div>
                            </div>

                            <!-- Languages Spoken -->
                            <div class="mb-4">
                                <span class="text-sm text-gray-500">Speaks</span>
                                <div id="speaksLanguages" class="flex flex-wrap gap-2 mt-1">
                                    <!-- Will be populated -->
                                </div>
                            </div>

                            <!-- Specialties -->
                            <div class="mb-4">
                                <span class="text-sm text-gray-500">Specialties</span>
                                <div id="specialties" class="flex flex-wrap gap-2 mt-1">
                                    <!-- Will be populated -->
                                </div>
                            </div>

                            <!-- Short Description -->
                            <p id="shortDescription" class="text-gray-700 leading-relaxed"></p>
                        </div>
                    </div>
                </div>

                <!-- Stats Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="stats-grid text-center">
                        <div>
                            <div class="flex items-center justify-center mb-2">
                                <span id="tutorRating" class="text-2xl text-yellow-500 mr-1"></span>
                                <span id="tutorRatingNumber" class="text-2xl font-bold text-gray-900"></span>
                            </div>
                            <div class="text-sm text-gray-500">Rating</div>
                        </div>
                        <div>
                            <div id="totalStudents" class="text-2xl font-bold text-gray-900 mb-2"></div>
                            <div class="text-sm text-gray-500">Students</div>
                        </div>
                        <div>
                            <div id="totalLessons" class="text-2xl font-bold text-gray-900 mb-2"></div>
                            <div class="text-sm text-gray-500">Lessons</div>
                        </div>
                        <div>
                            <div id="responseRate" class="text-2xl font-bold text-gray-900 mb-2">100%</div>
                            <div class="text-sm text-gray-500">Response rate</div>
                        </div>
                    </div>
                </div>

                <!-- Tabbed Content -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6">
                            <button class="tab-button active py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="about">
                                About Me
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="teacher">
                                Me as a Teacher
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="style">
                                My lessons & teaching style
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="resume">
                                Resume & Certificates
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <div id="about" class="tab-content active">
                            <div class="space-y-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">About Me</h3>
                                    <p id="aboutContent" class="text-gray-700 leading-relaxed mb-4"></p>
                                </div>
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 mb-3">Interests</h4>
                                    <div id="interests" class="flex flex-wrap gap-2">
                                        <!-- Interest tags will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="teacher" class="tab-content">
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">Me as a Teacher</h3>
                                <p id="teacherContent" class="text-gray-700 leading-relaxed"></p>
                            </div>
                        </div>
                        <div id="style" class="tab-content">
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">My Lessons & Teaching Style</h3>
                                <p id="styleContent" class="text-gray-700 leading-relaxed"></p>
                            </div>
                        </div>
                        <div id="resume" class="tab-content">
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">Resume & Certificates</h3>
                                <p id="resumeContent" class="text-gray-700 leading-relaxed"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Availability Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Availability</h2>
                        <div class="text-sm text-gray-500">
                            Click available slots to book a lesson
                        </div>
                    </div>
                    <div id="availabilityContainer">
                        <!-- Student booking interface will be generated here -->
                        <div class="text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto" style="border-color: #00C2B3;"></div>
                            <p class="text-gray-600 mt-2">Loading availability...</p>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-900"><span id="reviewCount">1,272</span> Reviews</h2>
                        <div class="flex space-x-2">
                            <button class="review-filter-btn active px-3 py-1 text-sm rounded-full bg-gray-800 text-white" data-filter="all">All</button>
                            <button class="review-filter-btn px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="english">English</button>
                            <button class="review-filter-btn px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="chinese">Chinese (Mandarin)</button>
                        </div>
                    </div>
                    <div id="reviewsContainer">
                        <!-- Reviews will be loaded here -->
                    </div>
                    <div class="text-center mt-6">
                        <button id="showMoreReviews" class="font-medium transition-colors" style="color: #00C2B3;" onmouseover="this.style.color='#00A89B'" onmouseout="this.style.color='#00C2B3'">
                            Show more ↓
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky-sidebar space-y-6">
                    <!-- Video Player -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div id="videoContainer" class="aspect-video bg-gray-900 flex items-center justify-center relative">
                            <!-- Video will be embedded here -->
                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <button class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all">
                                    <svg class="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 5v10l8-5-8-5z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Section -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <!-- Trial Lesson -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-lg font-medium text-gray-900">Trial Lesson</span>
                                <div class="flex items-center space-x-1">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-2xl font-bold text-gray-900 mb-4">USD <span id="trialPrice">8.00</span></div>
                            <button class="w-full bg-red-500 text-white py-3 px-4 rounded-lg hover:bg-red-600 transition-colors font-medium mb-3">
                                Book lesson
                            </button>
                        </div>

                        <!-- Regular Lesson -->
                        <div class="border-t pt-6">
                            <div class="text-lg font-medium text-gray-900 mb-2">Regular Lesson</div>
                            <div class="text-2xl font-bold text-gray-900 mb-4">USD <span id="regularPrice">12.00</span>+</div>
                            <div class="text-sm text-gray-500 mb-4">Package with 8% off</div>
                        </div>

                        <button onclick="contactTeacher()" class="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Contact teacher
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Modal -->
    <div id="bookingModal" class="modal-overlay">
        <div class="modal-content w-full max-w-4xl mx-4">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-gray-900">Book a Lesson</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 mt-2">Select your preferred date and time for the lesson</p>
            </div>

            <div class="p-6">
                <!-- Week Navigation -->
                <div class="flex items-center justify-between mb-6">
                    <button id="prevWeek" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <span>Previous Week</span>
                    </button>
                    <h3 id="weekRange" class="text-lg font-semibold text-gray-900"></h3>
                    <button id="nextWeek" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
                        <span>Next Week</span>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                <!-- Detailed Calendar -->
                <div id="detailedCalendar" class="detailed-calendar mb-6">
                    <!-- Calendar will be generated here -->
                </div>

                <!-- Selected Time Display -->
                <div id="selectedTimeDisplay" class="hidden bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">Selected Time</p>
                            <p id="selectedTimeText" class="text-green-600 font-semibold"></p>
                        </div>
                    </div>
                </div>

                <!-- Lesson Type Selection -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Lesson Type</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="lesson-type-card border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-300 transition-colors" data-type="trial">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium text-gray-900">Trial Lesson</h5>
                                <span class="text-lg font-bold text-gray-900">$<span id="modalTrialPrice">8.00</span></span>
                            </div>
                            <p class="text-sm text-gray-600">30 minutes • Perfect for first-time students</p>
                        </div>
                        <div class="lesson-type-card border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-green-300 transition-colors" data-type="regular">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium text-gray-900">Regular Lesson</h5>
                                <span class="text-lg font-bold text-gray-900">$<span id="modalRegularPrice">12.00</span></span>
                            </div>
                            <p class="text-sm text-gray-600">60 minutes • Comprehensive language learning</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button id="cancelBooking" class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                        Cancel
                    </button>
                    <button id="confirmBooking" class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        Book Lesson
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Get tutor ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const tutorId = urlParams.get('id');

        // DOM Elements
        const loadingState = document.getElementById('loadingState');
        const errorState = document.getElementById('errorState');
        const profileContent = document.getElementById('profileContent');

        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            if (!tutorId) {
                showError();
                return;
            }
            await loadTutorProfile();
            setupTabNavigation();
            setupReviewFilters();
            setupModalEventListeners();
        });

        // Load tutor profile
        async function loadTutorProfile() {
            try {
                console.log('Looking for tutor with ID:', tutorId);

                const { data, error } = await supabase
                    .from('tutors')
                    .select('*')
                    .eq('id', tutorId)
                    .single();

                console.log('Supabase response:', { data, error });

                if (error) {
                    console.error('Supabase error:', error);
                    // If tutor not found, try to get any tutor for demo
                    const { data: allTutors, error: allError } = await supabase
                        .from('tutors')
                        .select('*')
                        .limit(1);

                    if (allError || !allTutors || allTutors.length === 0) {
                        console.error('No tutors found in database');
                        showError();
                        return;
                    }

                    console.log('Using first available tutor for demo:', allTutors[0]);
                    await displayTutorProfile(allTutors[0]);
                } else if (!data) {
                    console.error('No data returned for tutor');
                    showError();
                    return;
                } else {
                    await displayTutorProfile(data);
                }

                await loadReviews();
                await initializeBookingSystem();
            } catch (error) {
                console.error('Error loading tutor profile:', error);
                showError();
            } finally {
                hideLoading();
            }
        }

        // Display tutor profile
        async function displayTutorProfile(tutor) {
            const avatarUrl = tutor.photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(tutor.name)}&background=6366f1&color=fff&size=150`;
            const rating = tutor.rating || 4.5;
            const ratingStars = '⭐'.repeat(Math.floor(rating));
            const languages = tutor.languages_spoken && tutor.languages_spoken.length > 0 ?
                (typeof tutor.languages_spoken === 'string' ? JSON.parse(tutor.languages_spoken) : tutor.languages_spoken) :
                [{ language: tutor.native_language || tutor.language, proficiency: 'Native' }];
            const tags = tutor.tags && tutor.tags.length > 0 ?
                (typeof tutor.tags === 'string' ? JSON.parse(tutor.tags) : tutor.tags) :
                ['Conversational', 'Grammar', 'Beginner Friendly'];

            // Update header information
            document.getElementById('tutorAvatar').src = avatarUrl;
            document.getElementById('tutorName').textContent = tutor.name;
            document.getElementById('tutorFlag').textContent = tutor.country_flag || '🇮🇳';

            // Show professional badge if applicable
            if (tutor.is_professional) {
                document.getElementById('professionalBadge').classList.remove('hidden');
            }

            // Update teaches languages
            const teachesLanguages = document.getElementById('teachesLanguages');
            teachesLanguages.innerHTML = `
                <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">
                    ${tutor.native_language || tutor.language} <span class="text-xs">Native</span>
                </span>
            `;

            // Update speaks languages
            const speaksLanguages = document.getElementById('speaksLanguages');
            speaksLanguages.innerHTML = languages.map(lang => {
                const isNative = lang.proficiency === 'Native' || lang.language === (tutor.native_language || tutor.language);
                return `<span class="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                    ${lang.language} <span class="text-xs">${lang.proficiency || (isNative ? 'Native' : 'Fluent')}</span>
                </span>`;
            }).join('');

            // Update specialties
            const specialties = document.getElementById('specialties');
            specialties.innerHTML = `
                <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                    </svg>
                    Business
                </span>
                <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Test Preparation
                </span>
            `;

            // Update short description
            document.getElementById('shortDescription').textContent =
                tutor.bio_headline || `Beginner - Advanced, Conversation, HSK, Business ${tutor.native_language || tutor.language}, The Writer of www.thechairmansbao.com`;

            // Update stats
            document.getElementById('tutorRating').textContent = ratingStars;
            document.getElementById('tutorRatingNumber').textContent = rating.toFixed(1);
            document.getElementById('totalStudents').textContent = tutor.total_students || Math.floor(Math.random() * 500 + 200);
            document.getElementById('totalLessons').textContent = tutor.total_lessons || Math.floor(Math.random() * 5000 + 1000);

            // Update tab content
            document.getElementById('aboutContent').textContent = tutor.about_me || tutor.bio ||
                `I have many interests and hobbies, such as playing basketball, playing ping-pong, traveling, watching NBA. I am a super fan of NBA. I went to America in March 2017, and stayed there for half a year. During this half a year, I visited many places in America, like Texas, Utah, Los Angeles, Las Vegas, Yellow Stone National Park, Grand Canyon. I like to talk about many different topics, such as travel, sports, movies, music, food, culture, business, etc.`;

            document.getElementById('teacherContent').textContent = tutor.me_as_teacher ||
                `I have been teaching ${tutor.native_language || tutor.language} for over 5 years. I am patient, friendly, and passionate about helping students achieve their language learning goals.`;

            document.getElementById('styleContent').textContent = tutor.teaching_style ||
                `My teaching style is interactive and student-centered. I focus on practical communication skills and tailor my lessons to each student's needs and interests.`;

            document.getElementById('resumeContent').textContent = tutor.resume ||
                `Bachelor's degree in ${tutor.native_language || tutor.language} Literature. Certified language teacher with 5+ years of experience. Specialized in business communication and test preparation.`;

            // Update interests
            const interests = document.getElementById('interests');
            const interestsList = ['Films & TV Series', 'Travel', 'History', 'Business & Finance', 'Sports & Fitness'];
            interests.innerHTML = interestsList.map(interest =>
                `<span class="interest-tag">${interest}</span>`
            ).join('');

            // Update pricing
            const trialPrice = (tutor.rate * 0.6).toFixed(2);
            const regularPrice = tutor.rate;
            document.getElementById('trialPrice').textContent = trialPrice;
            document.getElementById('regularPrice').textContent = regularPrice;

            // Update modal pricing
            document.getElementById('modalTrialPrice').textContent = trialPrice;
            document.getElementById('modalRegularPrice').textContent = regularPrice;

            // Update video
            const videoContainer = document.getElementById('videoContainer');
            if (tutor.video_url) {
                const videoId = extractYouTubeId(tutor.video_url);
                if (videoId) {
                    videoContainer.innerHTML = `
                        <iframe
                            width="100%"
                            height="100%"
                            src="https://www.youtube.com/embed/${videoId}"
                            frameborder="0"
                            allowfullscreen>
                        </iframe>
                    `;
                } else {
                    // Use a default video for demo
                    videoContainer.innerHTML = `
                        <iframe
                            width="100%"
                            height="100%"
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                            frameborder="0"
                            allowfullscreen>
                        </iframe>
                    `;
                }
            } else {
                // Use a default video for demo
                videoContainer.innerHTML = `
                    <iframe
                        width="100%"
                        height="100%"
                        src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                        frameborder="0"
                        allowfullscreen>
                    </iframe>
                `;
            }

            await initializeBookingSystem();
            profileContent.classList.remove('hidden');
        }

        // Extract YouTube video ID from URL
        function extractYouTubeId(url) {
            const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
            const match = url.match(regExp);
            return (match && match[2].length === 11) ? match[2] : null;
        }

        // Load reviews
        async function loadReviews() {
            try {
                const { data, error } = await supabase
                    .from('reviews')
                    .select('*')
                    .eq('tutor_id', tutorId)
                    .order('created_at', { ascending: false });

                if (error) throw error;

                displayReviews(data || []);
            } catch (error) {
                console.error('Error loading reviews:', error);
                document.getElementById('reviewsContainer').innerHTML = '<p class="text-gray-500">Unable to load reviews.</p>';
            }
        }

        // Display reviews
        function displayReviews(reviews) {
            const container = document.getElementById('reviewsContainer');

            // Sample reviews if none exist
            const sampleReviews = [
                {
                    student_name: 'Bryan',
                    lessons: 70,
                    language: 'Chinese (Mandarin)',
                    comment: 'I have been learning Chinese with Teacher Dan for almost eight months now. Today is my 50th lessons and we have finished HSK 6. His classes are always efficient, as he focuses on the key points without wasting any time, and I always feel that I get my money... Read more',
                    date: 'May 31, 2023',
                    isTeachersPick: true
                },
                {
                    student_name: 'Stephanie',
                    lessons: 230,
                    language: 'Chinese (Mandarin)',
                    comment: 'Another great lesson with Dai, as ever. Have been studying with him for over 18 months now and it\'s always instructive and fun. Thank you, Dai',
                    date: 'Feb 11, 2019',
                    isTeachersPick: true
                },
                {
                    student_name: 'Sergiy Turchyn',
                    lessons: 230,
                    language: 'Chinese (Mandarin)',
                    comment: 'Haven\'t written a review for a while. We are going through an HSK 5 textbook, almost done, focusing on vocabulary, text and exercises. Everything is good as usually.',
                    date: 'May 2, 2018',
                    isTeachersPick: true
                }
            ];

            const reviewsToShow = reviews.length > 0 ? reviews : sampleReviews;

            container.innerHTML = reviewsToShow.map(review => `
                <div class="border-b border-gray-200 pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-green-600 font-medium text-sm">
                                ${review.student_name ? review.student_name.charAt(0).toUpperCase() : 'B'}
                            </span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="font-medium text-gray-900">${review.student_name || 'Student'}</span>
                                ${review.isTeachersPick ? '<span class="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded-full">Teacher\'s pick</span>' : ''}
                            </div>
                            <div class="text-sm text-gray-500 mb-3">
                                ${review.lessons || 70} ${review.language || 'Chinese (Mandarin)'} lessons
                            </div>
                            <p class="text-gray-700 leading-relaxed mb-3">${review.comment || 'Great lesson!'}</p>
                            <div class="text-sm text-gray-500">${review.date || new Date().toLocaleDateString()}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Setup tab navigation
        function setupTabNavigation() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // Setup review filters
        function setupReviewFilters() {
            const filterButtons = document.querySelectorAll('.review-filter-btn');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-green-100', 'text-green-800');
                        btn.classList.add('bg-gray-100', 'text-gray-700');
                    });

                    // Add active class to clicked button
                    button.classList.add('active', 'bg-green-100', 'text-green-800');
                    button.classList.remove('bg-gray-100', 'text-gray-700');

                    // Filter reviews based on selection
                    const filter = button.getAttribute('data-filter');
                    // TODO: Implement actual filtering logic
                    console.log('Filter reviews by:', filter);
                });
            });
        }

        // Global variables for modal state
        let currentWeekStart = new Date();
        let selectedTimeSlot = null;
        let selectedLessonType = null;

        // Initialize booking system
        async function initializeBookingSystem() {
            try {
                console.log('🚀 [PROFILE] Initializing booking system...');

                // Get current user
                const { data: { session } } = await supabase.auth.getSession();
                const currentUser = session?.user;
                console.log('👤 [PROFILE] Current user:', currentUser?.id);

                // Get tutor ID from URL (this is the tutors table ID, not user_id)
                const urlParams = new URLSearchParams(window.location.search);
                const tutorRecordId = urlParams.get('id');
                console.log('🆔 [PROFILE] Tutor record ID from URL:', tutorRecordId);

                if (!tutorRecordId) {
                    console.error('❌ [PROFILE] No tutor ID found in URL');
                    return;
                }

                // Get the tutor's user_id from the tutors table
                console.log('🔍 [PROFILE] Looking up tutor user_id...');
                const { data: tutorData, error: tutorError } = await supabase
                    .from('tutors')
                    .select('user_id, name')
                    .eq('id', tutorRecordId)
                    .single();

                console.log('🔍 [PROFILE] Tutor lookup result:', { tutorData, tutorError });

                if (tutorError || !tutorData) {
                    console.error('❌ [PROFILE] Failed to get tutor user_id:', tutorError);
                    document.getElementById('availabilityContainer').innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-red-500 text-sm">Unable to find tutor information.</div>
                        </div>
                    `;
                    return;
                }

                const tutorUserId = tutorData.user_id;
                console.log('✅ [PROFILE] Found tutor user_id:', tutorUserId);

                // Initialize booking system with the correct user_id
                bookingSystem = new StudentBookingSystem(supabase);
                const success = await bookingSystem.initialize(tutorUserId, currentUser);

                if (success) {
                    console.log('✅ [PROFILE] Booking system initialized successfully');
                    // Render the availability grid
                    bookingSystem.renderAvailabilityGrid('availabilityContainer');


                } else {
                    console.error('❌ [PROFILE] Booking system initialization failed');
                    // Show error message
                    document.getElementById('availabilityContainer').innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-gray-500 text-sm">Unable to load availability. Please try again later.</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('💥 [PROFILE] Error initializing booking system:', error);
                document.getElementById('availabilityContainer').innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 text-sm">Error loading availability. Please refresh the page.</div>
                    </div>
                `;
            }
        }

        // Open booking modal
        function openBookingModal(dayIndex, timeSlot) {
            const modal = document.getElementById('bookingModal');
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Generate detailed calendar for the modal
            generateDetailedCalendar();

            // Pre-select the clicked time slot if provided
            if (dayIndex !== undefined && timeSlot !== undefined) {
                // We'll implement this after creating the detailed calendar
                setTimeout(() => {
                    selectTimeSlotInModal(dayIndex, timeSlot);
                }, 100);
            }
        }

        // Close booking modal
        function closeBookingModal() {
            const modal = document.getElementById('bookingModal');
            modal.classList.remove('active');
            document.body.style.overflow = '';
            selectedTimeSlot = null;
            selectedLessonType = null;
            updateBookingButton();
            document.getElementById('selectedTimeDisplay').classList.add('hidden');
        }

        // Generate detailed calendar for modal
        function generateDetailedCalendar() {
            const container = document.getElementById('detailedCalendar');
            const weekDays = ['Thu', 'Fri', 'Sat', 'Sun', 'Mon', 'Tue', 'Wed'];
            const dates = [];

            // Calculate dates for current week
            for (let i = 0; i < 7; i++) {
                const date = new Date(currentWeekStart);
                date.setDate(currentWeekStart.getDate() + i);
                dates.push({
                    day: date.getDate(),
                    month: date.getMonth(),
                    year: date.getFullYear()
                });
            }

            // Generate hourly time slots
            const timeSlots = [];
            for (let hour = 0; hour < 24; hour++) {
                const startTime = hour.toString().padStart(2, '0') + ':00';
                const endTime = (hour + 1).toString().padStart(2, '0') + ':00';
                timeSlots.push(`${startTime}`);
            }

            let gridHTML = '';

            // Header row
            gridHTML += '<div class="calendar-cell header"></div>';
            weekDays.forEach((day, index) => {
                const date = dates[index];
                const monthName = new Date(date.year, date.month).toLocaleDateString('en', { month: 'short' });
                gridHTML += `<div class="calendar-cell header">${day}<br><span class="text-xs">${monthName} ${date.day}</span></div>`;
            });

            // Time slot rows
            timeSlots.forEach((timeSlot, timeIndex) => {
                gridHTML += `<div class="calendar-cell time-label">${timeSlot}</div>`;
                weekDays.forEach((day, dayIndex) => {
                    const isAvailable = Math.random() > 0.4; // Random availability for demo
                    const cellClass = isAvailable ? 'available' : 'unavailable';
                    const dataAttrs = isAvailable ? `data-day="${dayIndex}" data-time="${timeIndex}" data-slot="${timeSlot}"` : '';
                    gridHTML += `<div class="calendar-cell ${cellClass}" ${dataAttrs}></div>`;
                });
            });

            container.innerHTML = gridHTML;

            // Add click handlers to available slots
            container.querySelectorAll('.calendar-cell.available').forEach(cell => {
                cell.addEventListener('click', () => {
                    selectDetailedTimeSlot(cell);
                });
            });

            // Update week range display
            updateWeekRangeDisplay();
        }

        // Select detailed time slot in modal
        function selectDetailedTimeSlot(cell) {
            // Remove previous selection
            document.querySelectorAll('.calendar-cell.selected').forEach(c => {
                c.classList.remove('selected');
                c.classList.add('available');
            });

            // Select new cell
            cell.classList.remove('available');
            cell.classList.add('selected');

            // Store selected time slot
            const dayIndex = parseInt(cell.dataset.day);
            const timeSlot = cell.dataset.slot;
            const weekDays = ['Thursday', 'Friday', 'Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday'];

            // Calculate the actual date
            const selectedDate = new Date(currentWeekStart);
            selectedDate.setDate(currentWeekStart.getDate() + dayIndex);

            selectedTimeSlot = {
                day: weekDays[dayIndex],
                date: selectedDate,
                time: timeSlot,
                dayIndex: dayIndex
            };

            // Update selected time display
            const selectedTimeDisplay = document.getElementById('selectedTimeDisplay');
            const selectedTimeText = document.getElementById('selectedTimeText');

            const dateStr = selectedDate.toLocaleDateString('en', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
            });

            selectedTimeText.textContent = `${dateStr} at ${timeSlot}`;
            selectedTimeDisplay.classList.remove('hidden');

            updateBookingButton();
        }

        // Select time slot in modal (for pre-selection from main grid)
        function selectTimeSlotInModal(dayIndex, timeSlot) {
            // Find the corresponding cell in detailed calendar
            const cells = document.querySelectorAll('.calendar-cell.available');
            cells.forEach(cell => {
                if (cell.dataset.day == dayIndex && cell.dataset.slot && cell.dataset.slot.includes(timeSlot.split(' - ')[0])) {
                    selectDetailedTimeSlot(cell);
                }
            });
        }

        // Update week range display
        function updateWeekRangeDisplay() {
            const weekRange = document.getElementById('weekRange');
            const startDate = new Date(currentWeekStart);
            const endDate = new Date(currentWeekStart);
            endDate.setDate(startDate.getDate() + 6);

            const startStr = startDate.toLocaleDateString('en', { month: 'short', day: 'numeric' });
            const endStr = endDate.toLocaleDateString('en', { month: 'short', day: 'numeric' });

            weekRange.textContent = `${startStr} - ${endStr}`;
        }

        // Navigate to previous week
        function navigateToPreviousWeek() {
            currentWeekStart.setDate(currentWeekStart.getDate() - 7);
            generateDetailedCalendar();
        }

        // Navigate to next week
        function navigateToNextWeek() {
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
            generateDetailedCalendar();
        }

        // Update booking button state
        function updateBookingButton() {
            const bookingButton = document.getElementById('confirmBooking');
            const canBook = selectedTimeSlot && selectedLessonType;

            bookingButton.disabled = !canBook;

            if (canBook) {
                bookingButton.textContent = `Book ${selectedLessonType} Lesson`;
            } else {
                bookingButton.textContent = 'Book Lesson';
            }
        }

        // Setup modal event listeners
        function setupModalEventListeners() {
            // Close modal handlers
            document.getElementById('closeModal').addEventListener('click', closeBookingModal);
            document.getElementById('cancelBooking').addEventListener('click', closeBookingModal);

            // Click outside modal to close
            document.getElementById('bookingModal').addEventListener('click', (e) => {
                if (e.target.id === 'bookingModal') {
                    closeBookingModal();
                }
            });

            // Week navigation
            document.getElementById('prevWeek').addEventListener('click', navigateToPreviousWeek);
            document.getElementById('nextWeek').addEventListener('click', navigateToNextWeek);

            // Lesson type selection
            document.querySelectorAll('.lesson-type-card').forEach(card => {
                card.addEventListener('click', () => {
                    // Remove previous selection
                    document.querySelectorAll('.lesson-type-card').forEach(c => {
                        c.classList.remove('border-green-500', 'bg-green-50');
                        c.classList.add('border-gray-200');
                    });

                    // Select new card
                    card.classList.remove('border-gray-200');
                    card.classList.add('border-green-500', 'bg-green-50');

                    selectedLessonType = card.dataset.type;
                    updateBookingButton();
                });
            });

            // Confirm booking
            document.getElementById('confirmBooking').addEventListener('click', () => {
                if (selectedTimeSlot && selectedLessonType) {
                    handleBookingConfirmation();
                }
            });

            // ESC key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeBookingModal();
                }
            });
        }

        // Handle booking confirmation
        function handleBookingConfirmation() {
            // Here you would typically send the booking data to your backend
            const bookingData = {
                tutorId: tutorId,
                date: selectedTimeSlot.date,
                time: selectedTimeSlot.time,
                lessonType: selectedLessonType,
                duration: selectedLessonType === 'trial' ? 30 : 60,
                price: selectedLessonType === 'trial' ?
                    document.getElementById('modalTrialPrice').textContent :
                    document.getElementById('modalRegularPrice').textContent
            };

            console.log('Booking confirmed:', bookingData);

            // Show success message
            alert(`Lesson booked successfully!\n\nDate: ${selectedTimeSlot.date.toLocaleDateString()}\nTime: ${selectedTimeSlot.time}\nType: ${selectedLessonType} lesson`);

            closeBookingModal();
        }

        // Contact teacher function
        async function contactTeacher() {
            try {
                console.log('📞 Contact teacher clicked for tutor ID:', tutorId);

                // Check if user is authenticated
                const { data: { session } } = await supabase.auth.getSession();
                if (!session) {
                    alert('Please log in to contact teachers');
                    window.location.href = 'index.html';
                    return;
                }

                console.log('✅ User authenticated:', session.user.id);

                // Get tutor data from the current profile
                const { data: tutorData, error } = await supabase
                    .from('tutors')
                    .select('user_id, name')
                    .eq('id', tutorId)
                    .single();

                console.log('📋 Tutor data query result:', { tutorData, error });

                if (error || !tutorData) {
                    console.error('❌ Failed to get tutor data:', error);
                    alert('Unable to contact teacher. Please try again.');
                    return;
                }

                console.log('✅ Found tutor:', tutorData);

                // Initialize messaging service
                const messaging = new SimpleMessaging(supabase);
                await messaging.initialize();

                // Get or create chat
                const chatId = await messaging.getOrCreateChat(tutorData.user_id);

                console.log('✅ Chat ID obtained:', chatId);

                // Small delay to ensure chat is created in database
                await new Promise(resolve => setTimeout(resolve, 500));

                // Redirect to student messages page
                const redirectUrl = `student-messages.html?chat=${chatId}`;
                console.log('🔄 Redirecting to:', redirectUrl);
                window.location.href = redirectUrl;

            } catch (error) {
                console.error('❌ Error contacting teacher:', error);
                alert('Failed to start conversation. Please try again.');
            }
        }







        // Utility functions
        function hideLoading() {
            loadingState.classList.add('hidden');
        }

        function showError() {
            loadingState.classList.add('hidden');
            errorState.classList.remove('hidden');
        }
    </script>

    <!-- Database Error Notification System -->
    <script src="database-error-notification.js?v=1"></script>

    <!-- Simple Messaging Service -->
    <script src="simple-messaging.js"></script>

    <!-- Enhanced Booking Modal (load first) -->
    <script src="enhanced-booking-modal.js?v=20"></script>

    <!-- Student Booking System (load after enhanced modal) -->
    <script src="student-booking.js?v=14"></script>
</body>
</html>
