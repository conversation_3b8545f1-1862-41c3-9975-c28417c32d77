/* CSS Custom Properties - Teal Professional Theme */
:root {
    /* Teal Professional Color Palette */
    --primary-color: #00C2B3;
    --primary-hover: #00A89B;
    --secondary-color: #007B83;
    --accent-color: #00A59C;
    --text-primary: #1E1E1E;
    --text-secondary: #5A5A5A;
    --text-light: #9E9E9E;
    --background: #FCFCFD;
    --background-light: #FFFFFF;
    --surface: #FFFFFF;
    --border-color: #E0E0E0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -4px rgba(0, 0, 0, 0.05);
    --gradient-primary: linear-gradient(135deg, #00C2B3 0%, #00A89B 100%);
    --gradient-secondary: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);
    --gradient-background: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);

    /* Special Colors */
    --star-rating: #FFC107;
    --availability: #38B000;
    --verified: #38B000;
    --accent-text: #00A59C;
    --success-light: #C7F4E5;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
    min-height: 100vh;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    padding: 0.75rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.nav-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Hero Section with Light Grey Background */
.hero {
    padding: 8rem 0 4rem;
    background: var(--gradient-background);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-features {
    list-style: none;
    margin-bottom: 3rem;
}

.hero-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.feature-icon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.cta-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    min-width: 250px;
    justify-content: center;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.google-icon {
    background: white;
    border-radius: 6px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hero Illustration */
.hero-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.illustration-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    width: 300px;
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.illustration-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
}

.tutor-info {
    flex: 1;
}

.tutor-name {
    height: 12px;
    background: var(--text-primary);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    width: 80%;
}

.tutor-lang {
    height: 8px;
    background: var(--text-light);
    border-radius: 4px;
    width: 60%;
}

.lesson-preview {
    height: 120px;
    background: var(--gradient-secondary);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.lesson-preview::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 4px;
    opacity: 0.3;
}

.lesson-preview::after {
    content: '';
    position: absolute;
    top: 40px;
    left: 20px;
    right: 60px;
    height: 6px;
    background: var(--text-light);
    border-radius: 3px;
    opacity: 0.5;
}

/* Languages Section with Cream Background */
.languages {
    padding: 4rem 0;
    background: var(--surface);
}

.languages-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.languages-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.language-pill {
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.language-pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.language-pill:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    background: var(--background-light);
}

.language-pill:hover::before {
    opacity: 1;
}

.language-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.tutor-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    /* Tablet styles - hide More dropdown but keep other nav items */
    .nav-dropdown {
        display: none;
    }
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 767px) {
    /* Mobile styles - show mobile menu button, hide desktop nav */
    .nav-container {
        padding: 0 1rem;
        position: relative;
    }

    .nav-links {
        display: none;
    }

    .nav-logo {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-size: 1.25rem;
    }

    .hero {
        padding: 4rem 0 2rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
        padding: 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    .hero-features {
        margin-bottom: 1.5rem;
        gap: 1rem;
    }

    .hero-features li {
        justify-content: center;
        font-size: 0.9rem;
        padding: 0.5rem;
    }

    .feature-icon {
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }

    .cta-button {
        width: 100%;
        max-width: 320px;
        margin: 0 auto;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
    }

    .languages-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .languages-title {
        font-size: 1.75rem;
        margin-bottom: 1.5rem;
    }

    .language-pill {
        padding: 0.75rem;
        text-align: center;
    }

    .language-name {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .tutor-count {
        font-size: 0.75rem;
    }

    /* Hero image adjustments */
    .hero-image {
        order: -1;
        margin-bottom: 1rem;
    }

    .hero-illustration {
        max-width: 280px;
        margin: 0 auto;
    }
}

/* Tablet Responsive Design */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-container {
        gap: 2rem;
        padding: 0 1.5rem;
    }

    .hero-title {
        font-size: 2.75rem;
    }

    .languages-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .cta-button {
        max-width: 280px;
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    .hero {
        padding: 3rem 0 1.5rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-features {
        gap: 0.75rem;
    }

    .hero-features li {
        font-size: 0.85rem;
        padding: 0.4rem;
    }

    .languages-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .language-pill {
        padding: 1rem;
    }

    .cta-button {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .languages-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-container {
        padding: 0 0.75rem;
    }
}

/* Login Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 1rem;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    width: 100%;
    max-width: 420px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95) translateY(20px);
    transition: all 0.3s ease;
    position: relative;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.modal-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.modal-close:hover {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.modal-subtitle {
    color: var(--text-secondary);
    margin: 0 0 2rem 0;
    font-size: 1rem;
    text-align: center;
}

.google-login-btn {
    width: 100%;
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.google-login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: 1;
    opacity: 0.1;
}

.google-login-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    color: var(--primary-color);
}

.google-login-btn:hover::before {
    left: 0;
}

.google-login-btn:active {
    transform: translateY(0);
}

.google-icon-wrapper {
    background: white;
    border-radius: 6px;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.modal-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.modal-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.modal-divider span {
    background: white;
    color: var(--text-light);
    padding: 0 1rem;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.alternative-options {
    text-align: center;
}

.coming-soon {
    color: var(--text-light);
    font-size: 0.875rem;
    font-style: italic;
    margin: 0;
    padding: 1rem;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    border-top: 1px solid var(--border-color);
}

.terms-text {
    font-size: 0.75rem;
    color: var(--text-light);
    text-align: center;
    line-height: 1.5;
    margin: 0;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Enhanced Modal Responsive Design */
@media (max-width: 767px) {
    .modal-overlay {
        padding: 1rem;
    }

    .modal-content {
        margin: 0;
        max-width: none;
        border-radius: 0.75rem;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 1.25rem 1.25rem 1rem;
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
    }

    .modal-title {
        font-size: 1.375rem;
        line-height: 1.3;
    }

    .modal-body {
        padding: 1.25rem;
    }

    .modal-footer {
        padding: 1rem 1.25rem 1.25rem;
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
    }

    .google-login-btn {
        width: 100%;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        font-weight: 600;
        justify-content: center;
    }

    .modal-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .terms-text {
        font-size: 0.8rem;
        line-height: 1.4;
    }
}

@media (max-width: 480px) {
    .modal-overlay {
        padding: 0.5rem;
    }

    .modal-content {
        border-radius: 0.5rem;
    }

    .modal-header {
        padding: 1rem 1rem 0.75rem;
    }

    .modal-title {
        font-size: 1.25rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 0.75rem 1rem 1rem;
    }

    .google-login-btn {
        padding: 1rem;
        font-size: 0.95rem;
    }
}
