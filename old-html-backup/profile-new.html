<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutor Profile - IndianTutors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tab-button.active { 
            border-bottom: 2px solid #4f46e5; 
            color: #4f46e5; 
            font-weight: 600;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
        }
        .calendar-cell {
            background-color: white;
            padding: 8px;
            min-height: 60px;
            border: 1px solid #e5e7eb;
        }
        .time-slot {
            font-size: 0.75rem;
            padding: 2px 4px;
            margin: 1px 0;
            border-radius: 4px;
            cursor: pointer;
        }
        .time-slot.available {
            background-color: #dcfce7;
            color: #166534;
        }
        .time-slot.unavailable {
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: not-allowed;
        }
        .sticky-sidebar {
            position: sticky;
            top: 2rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="home.html" class="text-2xl font-bold text-indigo-600">IndianTutors</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="findteacher.html" class="text-gray-600 hover:text-gray-900 flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <span>Back to Tutors</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Loading State -->
    <div id="loadingState" class="flex items-center justify-center min-h-screen">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p class="text-gray-600 mt-4">Loading tutor profile...</p>
        </div>
    </div>

    <!-- Error State -->
    <div id="errorState" class="hidden flex items-center justify-center min-h-screen">
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Tutor Not Found</h2>
            <p class="text-gray-600 mb-4">The tutor profile you're looking for doesn't exist.</p>
            <a href="findteacher.html" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                Browse Tutors
            </a>
        </div>
    </div>

    <!-- Profile Content -->
    <div id="profileContent" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Section (Main Content) -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Tutor Header -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-start space-x-4 mb-6">
                        <img id="tutorAvatar" src="" alt="Tutor" class="w-20 h-20 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h1 id="tutorName" class="text-2xl font-bold text-gray-900"></h1>
                                <span id="tutorFlag" class="text-2xl"></span>
                                <span id="professionalBadge" class="hidden bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Professional</span>
                            </div>
                            <p id="tutorTitle" class="text-lg text-gray-600 mb-2"></p>
                            <div class="flex items-center space-x-4 mb-3">
                                <div class="flex items-center space-x-1">
                                    <span id="tutorRating" class="text-yellow-500"></span>
                                    <span id="tutorRatingText" class="text-gray-600 font-medium"></span>
                                </div>
                                <div class="flex items-center space-x-1 text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    <span id="totalStudents"></span>
                                </div>
                                <div class="flex items-center space-x-1 text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <span id="totalLessons"></span>
                                </div>
                            </div>
                            <!-- Language Badges -->
                            <div id="languageBadges" class="flex flex-wrap gap-2 mb-4">
                                <!-- Language badges will be inserted here -->
                            </div>
                            <!-- Tags -->
                            <div id="tagBadges" class="flex flex-wrap gap-2">
                                <!-- Tag badges will be inserted here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bio Headline -->
                    <div class="border-t pt-4">
                        <p id="bioHeadline" class="text-lg text-gray-700 font-medium"></p>
                    </div>
                </div>

                <!-- Tabbed Content -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6">
                            <button class="tab-button active py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="about">
                                About Me
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="teacher">
                                Me as a Teacher
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="style">
                                Teaching Style
                            </button>
                            <button class="tab-button py-4 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="resume">
                                Resume
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <div id="about" class="tab-content active">
                            <p id="aboutContent" class="text-gray-700 leading-relaxed"></p>
                        </div>
                        <div id="teacher" class="tab-content">
                            <p id="teacherContent" class="text-gray-700 leading-relaxed"></p>
                        </div>
                        <div id="style" class="tab-content">
                            <p id="styleContent" class="text-gray-700 leading-relaxed"></p>
                        </div>
                        <div id="resume" class="tab-content">
                            <p id="resumeContent" class="text-gray-700 leading-relaxed"></p>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Student Reviews</h2>
                        <div class="flex space-x-2">
                            <button class="review-filter-btn active px-3 py-1 text-sm rounded-full bg-indigo-100 text-indigo-800" data-filter="all">All</button>
                            <button class="review-filter-btn px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="5">5 Stars</button>
                            <button class="review-filter-btn px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200" data-filter="helpful">Most Helpful</button>
                        </div>
                    </div>
                    <div id="reviewsContainer">
                        <!-- Reviews will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Right Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky-sidebar space-y-6">
                    <!-- Video Player -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Introduction Video</h3>
                        <div id="videoContainer" class="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                            <!-- Video will be embedded here -->
                        </div>
                    </div>

                    <!-- Booking Section -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="text-center mb-6">
                            <div class="text-3xl font-bold text-indigo-600 mb-2">₹<span id="sidebarRate"></span></div>
                            <div class="text-gray-600">per lesson</div>
                        </div>
                        
                        <button class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium mb-4">
                            Book Trial Lesson
                        </button>
                        
                        <button onclick="contactTeacher()" class="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Send Message
                        </button>
                    </div>

                    <!-- Schedule Calendar -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Times</h3>
                        <div id="calendarContainer">
                            <!-- Student booking interface will be generated here -->
                            <div class="text-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                                <p class="text-gray-600 mt-2">Loading availability...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Get tutor ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const tutorId = urlParams.get('id');

        // DOM Elements
        const loadingState = document.getElementById('loadingState');
        const errorState = document.getElementById('errorState');
        const profileContent = document.getElementById('profileContent');

        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            if (!tutorId) {
                showError();
                return;
            }
            await loadTutorProfile();
            setupTabNavigation();
            setupReviewFilters();
        });

        // Load tutor profile
        async function loadTutorProfile() {
            try {
                const { data, error } = await supabase
                    .from('tutors')
                    .select('*')
                    .eq('id', tutorId)
                    .single();

                if (error || !data) {
                    showError();
                    return;
                }

                await displayTutorProfile(data);
                await loadReviews();
                await initializeBookingSystem();
            } catch (error) {
                console.error('Error loading tutor profile:', error);
                showError();
            } finally {
                hideLoading();
            }
        }

        // Display tutor profile
        async function displayTutorProfile(tutor) {
            const avatarUrl = tutor.photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(tutor.name)}&background=6366f1&color=fff&size=150`;
            const rating = tutor.rating || 0;
            const ratingStars = '⭐'.repeat(Math.floor(rating));
            const languages = tutor.languages_spoken ? JSON.parse(tutor.languages_spoken) : [];
            const tags = tutor.tags ? JSON.parse(tutor.tags) : [];

            // Update header information
            document.getElementById('tutorAvatar').src = avatarUrl;
            document.getElementById('tutorName').textContent = tutor.name;
            document.getElementById('tutorFlag').textContent = tutor.country_flag || '🇮🇳';
            document.getElementById('tutorTitle').textContent = tutor.bio_headline || `${tutor.native_language} Teacher`;
            document.getElementById('tutorRating').textContent = ratingStars;
            document.getElementById('tutorRatingText').textContent = `${rating.toFixed(1)} (${tutor.total_students || 0} reviews)`;
            document.getElementById('totalStudents').textContent = `${tutor.total_students || 0} students`;
            document.getElementById('totalLessons').textContent = `${tutor.total_lessons || 0} lessons`;
            document.getElementById('bioHeadline').textContent = tutor.bio_headline || '';
            document.getElementById('sidebarRate').textContent = tutor.rate;

            // Show professional badge if applicable
            if (tutor.is_professional) {
                document.getElementById('professionalBadge').classList.remove('hidden');
            }

            // Update language badges
            const languageBadges = document.getElementById('languageBadges');
            languageBadges.innerHTML = `
                <span class="bg-indigo-100 text-indigo-800 text-sm px-3 py-1 rounded-full font-medium">${tutor.native_language}</span>
                ${languages.map(lang =>
                    `<span class="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">${lang.language} (${lang.proficiency})</span>`
                ).join('')}
            `;

            // Update tag badges
            const tagBadges = document.getElementById('tagBadges');
            tagBadges.innerHTML = tags.map(tag =>
                `<span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">${tag}</span>`
            ).join('');

            // Update tab content
            document.getElementById('aboutContent').textContent = tutor.about_me || tutor.bio || 'No information available.';
            document.getElementById('teacherContent').textContent = tutor.me_as_teacher || 'No information available.';
            document.getElementById('styleContent').textContent = tutor.teaching_style || 'No information available.';
            document.getElementById('resumeContent').textContent = tutor.resume || 'No information available.';

            // Update video
            const videoContainer = document.getElementById('videoContainer');
            if (tutor.video_url) {
                const videoId = extractYouTubeId(tutor.video_url);
                if (videoId) {
                    videoContainer.innerHTML = `
                        <iframe
                            width="100%"
                            height="100%"
                            src="https://www.youtube.com/embed/${videoId}"
                            frameborder="0"
                            allowfullscreen
                            class="rounded-lg">
                        </iframe>
                    `;
                } else {
                    videoContainer.innerHTML = `
                        <div class="text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            <p>Introduction video coming soon</p>
                        </div>
                    `;
                }
            } else {
                videoContainer.innerHTML = `
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <p>Introduction video coming soon</p>
                    </div>
                `;
            }

            profileContent.classList.remove('hidden');
        }

        // Extract YouTube video ID from URL
        function extractYouTubeId(url) {
            const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
            const match = url.match(regExp);
            return (match && match[2].length === 11) ? match[2] : null;
        }

        // Load reviews
        async function loadReviews() {
            try {
                const { data, error } = await supabase
                    .from('reviews')
                    .select('*')
                    .eq('tutor_id', tutorId)
                    .order('created_at', { ascending: false });

                if (error) throw error;

                displayReviews(data || []);
            } catch (error) {
                console.error('Error loading reviews:', error);
                document.getElementById('reviewsContainer').innerHTML = '<p class="text-gray-500">Unable to load reviews.</p>';
            }
        }

        // Display reviews
        function displayReviews(reviews) {
            const container = document.getElementById('reviewsContainer');

            if (reviews.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No reviews yet.</p>';
                return;
            }

            container.innerHTML = reviews.map(review => `
                <div class="border-b border-gray-200 pb-4 mb-4 last:border-b-0 last:pb-0 last:mb-0">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span class="text-indigo-600 font-medium text-sm">
                                ${review.student_id ? review.student_id.substring(0, 2).toUpperCase() : 'ST'}
                            </span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-yellow-500">${'⭐'.repeat(review.rating)}</span>
                                <span class="text-sm text-gray-500">${new Date(review.created_at).toLocaleDateString()}</span>
                            </div>
                            <p class="text-gray-700">${review.comment || 'No comment provided.'}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Setup tab navigation
        function setupTabNavigation() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // Setup review filters
        function setupReviewFilters() {
            const filterButtons = document.querySelectorAll('.review-filter-btn');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-indigo-100', 'text-indigo-800');
                        btn.classList.add('bg-gray-100', 'text-gray-700');
                    });

                    // Add active class to clicked button
                    button.classList.add('active', 'bg-indigo-100', 'text-indigo-800');
                    button.classList.remove('bg-gray-100', 'text-gray-700');

                    // Filter reviews based on selection
                    const filter = button.getAttribute('data-filter');
                    // TODO: Implement actual filtering logic
                    console.log('Filter reviews by:', filter);
                });
            });
        }

        // Initialize booking system
        async function initializeBookingSystem() {
            try {
                console.log('🚀 [PROFILE-NEW] Initializing booking system...');

                // Get current user
                const { data: { session } } = await supabase.auth.getSession();
                const currentUser = session?.user;
                console.log('👤 [PROFILE-NEW] Current user:', currentUser?.id);

                // Get tutor record ID from URL
                console.log('🆔 [PROFILE-NEW] Tutor record ID from URL:', tutorId);

                if (!tutorId) {
                    console.error('❌ [PROFILE-NEW] No tutor ID found');
                    return;
                }

                // Get the tutor's user_id from the tutors table
                console.log('🔍 [PROFILE-NEW] Looking up tutor user_id...');
                const { data: tutorData, error: tutorError } = await supabase
                    .from('tutors')
                    .select('user_id, name')
                    .eq('id', tutorId)
                    .single();

                console.log('🔍 [PROFILE-NEW] Tutor lookup result:', { tutorData, tutorError });

                if (tutorError || !tutorData) {
                    console.error('❌ [PROFILE-NEW] Failed to get tutor user_id:', tutorError);
                    document.getElementById('calendarContainer').innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-red-500 text-sm">Unable to find tutor information.</div>
                        </div>
                    `;
                    return;
                }

                const tutorUserId = tutorData.user_id;
                console.log('✅ [PROFILE-NEW] Found tutor user_id:', tutorUserId);

                // Initialize booking system with the correct user_id
                bookingSystem = new StudentBookingSystem(supabase);
                const success = await bookingSystem.initialize(tutorUserId, currentUser);

                if (success) {
                    console.log('✅ [PROFILE-NEW] Booking system initialized successfully');
                    // Render the availability grid
                    bookingSystem.renderAvailabilityGrid('calendarContainer');
                } else {
                    console.error('❌ [PROFILE-NEW] Booking system initialization failed');
                    // Show error message
                    document.getElementById('calendarContainer').innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-gray-500 text-sm">Unable to load availability. Please try again later.</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('💥 [PROFILE-NEW] Error initializing booking system:', error);
                document.getElementById('calendarContainer').innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 text-sm">Error loading availability. Please refresh the page.</div>
                    </div>
                `;
            }
        }

        // Contact teacher function
        async function contactTeacher() {
            try {
                console.log('📞 Contact teacher clicked for tutor ID:', tutorId);

                // Check if user is authenticated
                const { data: { session } } = await supabase.auth.getSession();
                if (!session) {
                    alert('Please log in to contact teachers');
                    window.location.href = 'index.html';
                    return;
                }

                console.log('✅ User authenticated:', session.user.id);

                // Get tutor data from the current profile
                const { data: tutorData, error } = await supabase
                    .from('tutors')
                    .select('user_id, name')
                    .eq('id', tutorId)
                    .single();

                console.log('📋 Tutor data query result:', { tutorData, error });

                if (error || !tutorData) {
                    console.error('❌ Failed to get tutor data:', error);
                    alert('Unable to contact teacher. Please try again.');
                    return;
                }

                console.log('✅ Found tutor:', tutorData);

                // Initialize messaging service
                const messaging = new SimpleMessaging(supabase);
                await messaging.initialize();

                // Get or create chat
                const chatId = await messaging.getOrCreateChat(tutorData.user_id);

                console.log('✅ Chat ID obtained:', chatId);

                // Small delay to ensure chat is created in database
                await new Promise(resolve => setTimeout(resolve, 500));

                // Redirect to student messages page
                const redirectUrl = `student-messages.html?chat=${chatId}`;
                console.log('🔄 Redirecting to:', redirectUrl);
                window.location.href = redirectUrl;

            } catch (error) {
                console.error('❌ Error contacting teacher:', error);
                alert('Failed to start conversation. Please try again.');
            }
        }

        // Utility functions
        function hideLoading() {
            loadingState.classList.add('hidden');
        }

        function showError() {
            loadingState.classList.add('hidden');
            errorState.classList.remove('hidden');
        }
    </script>

    <!-- Simple Messaging Service -->
    <script src="simple-messaging.js"></script>

    <!-- Enhanced Booking Modal -->
    <script src="enhanced-booking-modal.js?v=15"></script>

    <!-- Student Booking System -->
    <script src="student-booking.js?v=8"></script>
</body>
</html>
