/* Home Page Specific Styles */

/* Navigation Updates for Authenticated State */
.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

/* Dropdown Styles */
.nav-dropdown, .profile-dropdown {
    position: relative;
}

.nav-dropdown-btn, .profile-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.nav-dropdown-btn:hover, .profile-btn:hover {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.profile-btn {
    gap: 0.75rem;
    padding: 0.5rem 1rem;
}

.profile-avatar, .dropdown-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-name {
    font-weight: 500;
    color: var(--text-primary);
}

.nav-dropdown-content, .profile-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.nav-dropdown:hover .nav-dropdown-content,
.profile-dropdown:hover .profile-dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-dropdown-content a, .profile-dropdown-content a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
}

.nav-dropdown-content a:hover, .profile-dropdown-content a:hover {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.profile-info {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dropdown-user-info {
    display: flex;
    flex-direction: column;
}

.dropdown-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.dropdown-email {
    color: var(--text-light);
    font-size: 0.75rem;
}

.dropdown-divider {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 0;
}

.logout-btn {
    width: 100%;
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    color: #ef4444;
    cursor: pointer;
    text-align: left;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: #fef2f2;
}

/* Main Content */
.main-content {
    padding-top: 5rem;
    min-height: 100vh;
    background: var(--background);
}

.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 1.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-all-link:hover {
    text-decoration: underline;
}

.card-content {
    padding: 1.5rem;
}

/* Profile Card */
.profile-info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-details h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
}

.profile-stats {
    display: flex;
    gap: 1.5rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

/* Lessons Card */
.lesson-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-time {
    display: flex;
    flex-direction: column;
    min-width: 80px;
}

.lesson-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.lesson-hour {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.lesson-details {
    flex: 1;
    margin-left: 1rem;
}

.lesson-language {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
}

.lesson-duration {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.lesson-join-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lesson-join-btn:hover {
    background: var(--primary-hover);
}

.no-lessons {
    text-align: center;
    padding: 2rem 0;
}

.no-lessons p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.book-lesson-btn {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.2s ease;
}

.book-lesson-btn:hover {
    background: var(--primary-hover);
}

/* Teachers Section */
.teachers-section {
    margin-top: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.teachers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.teacher-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.teacher-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.teacher-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.teacher-info {
    flex: 1;
}

.teacher-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.teacher-language {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
}

.teacher-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.rating {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.stars {
    color: #fbbf24;
    font-size: 0.75rem;
}

.teacher-rate {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.875rem;
    margin: 0;
}

.book-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.book-btn:hover {
    background: var(--primary-hover);
}

/* Mobile Sidebar Styles */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100%;
    background: white;
    z-index: 1999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-sidebar.active {
    transform: translateX(0);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
}

.sidebar-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.sidebar-user-info {
    flex: 1;
    min-width: 0;
}

.sidebar-name {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.sidebar-email {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sidebar-content {
    padding: 1rem 0;
}

.sidebar-section {
    padding: 0 1rem;
    margin-bottom: 1rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    font-weight: 500;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 0.95rem;
}

.sidebar-link:hover {
    background: var(--background);
    color: var(--text-primary);
}

.sidebar-link svg {
    flex-shrink: 0;
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.sidebar-link:hover svg {
    color: var(--primary-color);
}

.trial-link {
    background: linear-gradient(135deg, #38B000 0%, #2E8B00 100%);
    color: white !important;
    position: relative;
    overflow: hidden;
}

.trial-link:hover {
    background: linear-gradient(135deg, #2E8B00 0%, #1F5F00 100%);
    color: white !important;
}

.trial-link svg {
    color: white !important;
}

.trial-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: auto;
}

.sidebar-divider {
    height: 1px;
    background: var(--border-color);
    margin: 1rem 0;
    border: none;
}

.logout-link {
    color: #dc2626 !important;
}

.logout-link:hover {
    background: rgba(220, 38, 38, 0.1) !important;
    color: #dc2626 !important;
}

.logout-link svg {
    color: #dc2626 !important;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.mobile-menu-btn:hover {
    background: var(--background);
}

.mobile-profile-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

/* Responsive Design */
@media (max-width: 1024px) {
    /* Tablet styles */
    .nav-dropdown {
        display: none;
    }

    .nav-links {
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    /* Mobile styles */
    .mobile-menu-btn {
        display: block;
    }

    .nav-links {
        display: none;
    }

    .nav-logo {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .main-content {
        padding: 1rem;
        margin-top: 70px;
    }

    .welcome-section {
        text-align: center;
        margin-bottom: 2rem;
    }

    .welcome-title {
        font-size: 1.75rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .profile-stats {
        gap: 1rem;
    }

    .teachers-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .teacher-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .mobile-sidebar {
        width: 100%;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-logo h2 {
        font-size: 1.25rem;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-avatar {
        width: 40px;
        height: 40px;
    }
}
