/* GLOBAL RESPONSIVE DESIGN SYSTEM FOR TUTOR MARKETPLACE */
/* Mobile-First Approach with Consistent Breakpoints */

/* CSS Variables for Consistent Spacing, Sizing, and Indian Heritage Color Theme */
:root {
    /* Breakpoints */
    --mobile-max: 767px;
    --tablet-min: 768px;
    --tablet-max: 1023px;
    --desktop-min: 1024px;

    /* Touch-friendly sizes */
    --touch-target-min: 44px;
    --touch-target-comfortable: 48px;

    /* Responsive spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Container widths */
    --container-mobile: 100%;
    --container-tablet: 768px;
    --container-desktop: 1200px;

    /* Font sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* COMPREHENSIVE TEAL PROFESSIONAL COLOR THEME */
    /* Primary Colors - Teal Professional Accents */
    --color-primary: #00C2B3;
    --color-primary-light: #00D4C4;
    --color-primary-dark: #00A89B;
    --color-primary-darker: #008E82;

    /* Secondary Colors - Teal/Blue-Grey */
    --color-secondary: #007B83;
    --color-secondary-light: #009AA3;
    --color-secondary-dark: #006B72;
    --color-secondary-darker: #005B61;

    /* Accent Colors - Warm Teal Touches */
    --color-accent: #00A59C;
    --color-accent-light: #00C2B7;
    --color-accent-dark: #008E86;
    --color-accent-darker: #007770;

    /* Background Colors - White Mist Theme */
    --color-background: #FCFCFD;
    --color-background-light: #FFFFFF;
    --color-background-dark: #F5F6FA;
    --color-surface: #FFFFFF;
    --color-surface-light: #FEFEFE;
    --color-surface-dark: #F8F9FA;

    /* Text Colors - italki-Inspired Grey Scale */
    --color-text-primary: #1E1E1E;
    --color-text-secondary: #5A5A5A;
    --color-text-tertiary: #9E9E9E;
    --color-text-light: #CCCCCC;
    --color-text-inverse: #FFFFFF;

    /* Status Colors (italki-Inspired Theme) */
    --color-success: #38B000;
    --color-success-light: #4CAF50;
    --color-success-dark: #2E8B00;

    --color-warning: #FFC107;
    --color-warning-light: #FFD54F;
    --color-warning-dark: #FFA000;

    --color-error: #DC2626;
    --color-error-light: #EF4444;
    --color-error-dark: #B91C1C;

    --color-info: #00A59C;
    --color-info-light: #00C2B7;
    --color-info-dark: #008E86;

    /* Special Colors */
    --color-star-rating: #FFC107;
    --color-availability: #38B000;
    --color-verified: #38B000;
    --color-accent-text: #00A59C;

    /* Border Colors - Light Grey Tones */
    --color-border: #E0E0E0;
    --color-border-light: #F5F5F5;
    --color-border-dark: #BDBDBD;

    /* Shadow Colors - Light Grey Theme */
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.15);

    /* Gradient Combinations */
    --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
    --gradient-background: linear-gradient(135deg, var(--color-background) 0%, var(--color-background-dark) 100%);
}

/* GLOBAL RESPONSIVE UTILITIES */

/* Container System */
.responsive-container {
    width: 100%;
    max-width: var(--container-desktop);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (max-width: 767px) {
    .responsive-container {
        padding: 0 var(--spacing-sm);
    }
}

@media (min-width: 768px) {
    .responsive-container {
        padding: 0 var(--spacing-lg);
    }
}

/* NAVIGATION RESPONSIVE PATTERNS WITH INDIAN HERITAGE THEME */
.responsive-nav {
    position: sticky;
    top: 0;
    z-index: 50;
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    box-shadow: 0 2px 8px var(--shadow-light);
    backdrop-filter: blur(8px);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 64px;
    padding: 0 var(--spacing-md);
}

.nav-logo {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--color-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.nav-logo:hover {
    color: var(--color-primary-dark);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    min-height: var(--touch-target-min);
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.nav-link:hover {
    color: var(--color-primary);
    background: var(--color-background-light);
}

.nav-link.active {
    color: var(--color-primary);
    background: var(--color-background);
    font-weight: 600;
}

/* Mobile Navigation */
@media (max-width: 767px) {
    .nav-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        min-height: auto;
    }
    
    .nav-left {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .nav-right {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--text-sm);
    }
    
    .nav-logo {
        font-size: var(--text-lg);
    }
    
    .nav-links {
        gap: var(--spacing-md);
    }
}

/* BUTTON SYSTEM WITH INDIAN HERITAGE THEME */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: var(--touch-target-min);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--text-sm);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--color-text-inverse);
    border: 1px solid var(--color-primary);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.btn-primary:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.btn-secondary {
    background: var(--color-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background: var(--color-secondary-dark);
    color: var(--color-text-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-danger {
    background: var(--color-error);
    color: var(--color-text-inverse);
    border: 1px solid var(--color-error);
}

.btn-danger:hover {
    background: var(--color-error-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.btn-success {
    background: var(--gradient-accent);
    color: var(--color-text-inverse);
    border: 1px solid var(--color-success);
}

.btn-success:hover {
    background: var(--color-success-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-warning {
    background: var(--color-warning);
    color: var(--color-text-inverse);
    border: 1px solid var(--color-warning);
}

.btn-warning:hover {
    background: var(--color-warning-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 153, 51, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
}

.btn-outline:hover {
    background: var(--color-primary);
    color: var(--color-text-inverse);
}

/* Mobile button adjustments */
@media (max-width: 767px) {
    .btn {
        min-height: var(--touch-target-comfortable);
        padding: 0.75rem 1rem;
        font-size: var(--text-base);
        font-weight: 600;
    }
    
    .btn-full-mobile {
        width: 100%;
        justify-content: center;
    }
    
    .btn-stack-mobile {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* CARD SYSTEM WITH INDIAN HERITAGE THEME */
.card {
    background: var(--color-surface);
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px var(--shadow-light);
    border: 1px solid var(--color-border);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 28px var(--shadow-medium);
    border-color: var(--color-primary-light);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-light);
}

.card-body {
    padding: var(--spacing-lg);
    background: var(--color-surface);
}

.card-footer {
    padding: var(--spacing-lg);
    background: var(--color-background-light);
    border-top: 1px solid var(--color-border);
}

.card-title {
    color: var(--color-text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-subtitle {
    color: var(--color-text-secondary);
    font-size: var(--text-sm);
}

.card-text {
    color: var(--color-text-primary);
    line-height: 1.6;
}

/* Mobile card adjustments */
@media (max-width: 767px) {
    .card {
        margin: 0 calc(-1 * var(--spacing-sm));
        border-radius: 0.5rem;
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-md);
    }
    
    .card-mobile-stack {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
}

/* GRID SYSTEM */
.responsive-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Mobile grid adjustments */
@media (max-width: 767px) {
    .responsive-grid {
        gap: var(--spacing-md);
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .grid-mobile-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet grid adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
    .grid-3,
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-tablet-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* FORM SYSTEM WITH INDIAN HERITAGE THEME */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    min-height: var(--touch-target-min);
    padding: 0.75rem;
    border: 2px solid var(--color-border);
    border-radius: 0.5rem;
    font-size: var(--text-base);
    background: var(--color-surface);
    color: var(--color-text-primary);
    transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px var(--shadow-light);
    background: var(--color-surface-light);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--color-primary-light);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--color-text-tertiary);
}

.form-error {
    color: var(--color-error);
    font-size: var(--text-sm);
    margin-top: var(--spacing-xs);
}

.form-success {
    color: var(--color-success);
    font-size: var(--text-sm);
    margin-top: var(--spacing-xs);
}

/* Mobile form adjustments */
@media (max-width: 767px) {
    .form-input {
        min-height: var(--touch-target-comfortable);
        font-size: var(--text-base);
    }
}

/* MODAL SYSTEM WITH INDIAN HERITAGE THEME */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(51, 51, 51, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    padding: var(--spacing-md);
    animation: modalFadeIn 0.2s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

.modal-content {
    background: var(--color-surface);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px var(--shadow-dark);
    border: 1px solid var(--color-border);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-light);
}

.modal-title {
    color: var(--color-text-primary);
    font-weight: 600;
    font-size: var(--text-lg);
}

.modal-body {
    padding: var(--spacing-lg);
    background: var(--color-surface);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    background: var(--color-surface-light);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Mobile modal adjustments */
@media (max-width: 767px) {
    .modal-overlay {
        padding: var(--spacing-sm);
    }
    
    .modal-content {
        max-width: none;
        border-radius: 0.5rem;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* ACCESSIBILITY IMPROVEMENTS */
@media (prefers-reduced-motion: reduce) {
    .card,
    .btn,
    .nav-link,
    .form-input {
        transition: none;
    }
    
    .card:hover,
    .btn:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card,
    .form-input {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
        border-style: solid;
    }
    
    .btn-primary {
        border-color: #4f46e5;
    }
    
    .btn-secondary {
        border-color: #374151;
    }
}

/* STATUS BADGES WITH INDIAN HERITAGE THEME */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-success {
    background: var(--color-success);
    color: var(--color-text-inverse);
}

.badge-warning {
    background: var(--color-warning);
    color: var(--color-text-inverse);
}

.badge-error {
    background: var(--color-error);
    color: var(--color-text-inverse);
}

.badge-info {
    background: var(--color-info);
    color: var(--color-text-inverse);
}

.badge-primary {
    background: var(--color-primary);
    color: var(--color-text-inverse);
}

.badge-secondary {
    background: var(--color-secondary);
    color: var(--color-text-inverse);
}

.badge-outline {
    background: transparent;
    border: 1px solid var(--color-border-dark);
    color: var(--color-text-secondary);
}

/* UTILITY CLASSES WITH INDIAN HERITAGE THEME */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-accent { color: var(--color-accent); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-muted { color: var(--color-text-tertiary); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-accent { background-color: var(--color-accent); }
.bg-surface { background-color: var(--color-surface); }
.bg-background { background-color: var(--color-background); }

.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }
.border-accent { border-color: var(--color-accent); }

/* LINK STYLES WITH INDIAN HERITAGE THEME */
.link {
    color: var(--color-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.link:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

.link-secondary {
    color: var(--color-secondary);
}

.link-secondary:hover {
    color: var(--color-secondary-dark);
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .nav-link,
    .form-input {
        min-height: var(--touch-target-comfortable);
    }

    .card:hover {
        transform: none;
        box-shadow: 0 2px 8px var(--shadow-light);
    }

    .card:hover::before {
        opacity: 1;
    }
}
