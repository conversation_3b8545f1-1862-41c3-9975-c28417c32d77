<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - Student Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .chat-container { height: calc(100vh - 200px); }
        .messages-container { height: calc(100% - 80px); }
        .message-bubble {
            max-width: 70%;
            word-wrap: break-word;
        }
        .message-sent {
            background: linear-gradient(135deg, #00C2B3 0%, #00A89B 100%);
        }
        .message-received {
            background: #f1f5f9;
            color: #334155;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <a href="#" class="text-2xl font-bold" style="color: #00C2B3;">IndianTutors</a>
                    <button onclick="goBackToStudentDashboard()" class="text-gray-600 transition-colors" style="color: #5A5A5A;" onmouseover="this.style.color='#00C2B3'" onmouseout="this.style.color='#5A5A5A'">
                        ← Back to Dashboard
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="subscriptionIndicator" class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span class="text-xs text-gray-500">Connecting...</span>
                    </div>
                    <span id="userEmail" class="text-gray-600">Loading...</span>
                    <button onclick="handleLogout()" class="text-red-600 hover:text-red-700">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Loading State -->
    <div id="loadingState" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto" style="border-color: #00C2B3;"></div>
            <p class="text-gray-600 mt-4">Loading messages...</p>
        </div>
    </div>

    <!-- Error State -->
    <div id="errorState" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 hidden">
        <div class="text-center">
            <div class="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 class="text-xl font-semibold text-gray-900 mb-2">Failed to Load Messages</h2>
            <p class="text-gray-600 mb-4">Please check your connection and try again.</p>
            <button onclick="window.location.reload()" class="text-white px-4 py-2 rounded-lg transition-colors" style="background-color: #00C2B3;" onmouseover="this.style.backgroundColor='#00A89B'" onmouseout="this.style.backgroundColor='#00C2B3'">
                Retry
            </button>
        </div>
    </div>

    <!-- Messages Container -->
    <div id="messagesContainer" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 hidden">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="flex chat-container" style="height: 600px;">
                <!-- Chat List Sidebar -->
                <div class="w-1/3 border-r border-gray-200 bg-gray-50">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">My Tutors</h2>
                    </div>
                    <div id="chatsList" class="overflow-y-auto h-full">
                        <!-- Chats will be loaded here -->
                    </div>
                </div>

                <!-- Chat Messages Area -->
                <div class="flex-1 flex flex-col">
                    <!-- No Chat Selected -->
                    <div id="noChatSelected" class="flex-1 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-gray-400 text-6xl mb-4">💬</div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                            <p class="text-gray-500">Choose a tutor from the list to start messaging</p>
                        </div>
                    </div>

                    <!-- Chat Header -->
                    <div id="chatHeader" class="p-4 border-b border-gray-200 bg-white hidden">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center" style="background-color: rgba(0, 194, 179, 0.1);">
                                <span class="font-semibold" style="color: #00C2B3;" id="chatUserInitial">T</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900" id="chatUserName">Tutor Name</h3>
                                <p class="text-sm text-gray-500">Tutor</p>
                            </div>
                        </div>
                    </div>

                    <!-- Messages Area -->
                    <div id="messagesArea" class="flex-1 overflow-y-auto p-4 messages-container hidden">
                        <div id="messagesList" class="space-y-4">
                            <!-- Messages will be loaded here -->
                        </div>
                    </div>

                    <!-- Message Input -->
                    <div id="messageInput" class="p-4 border-t border-gray-200 bg-white hidden">
                        <div class="flex space-x-3">
                            <input 
                                type="text" 
                                id="messageText" 
                                placeholder="Type your message..." 
                                class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:border-transparent"
                                style="--tw-ring-color: #00C2B3;"
                                onkeypress="handleMessageKeyPress(event)"
                            >
                            <button 
                                onclick="sendMessage()" 
                                class="text-white px-6 py-2 rounded-lg transition-colors font-medium"
                                style="background-color: #00C2B3;"
                                onmouseover="this.style.backgroundColor='#00A89B'"
                                onmouseout="this.style.backgroundColor='#00C2B3'"
                            >
                                Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Simple Messaging Service -->
    <script src="simple-messaging.js?v=2"></script>

    <!-- Main Messages Script -->
    <script src="messages-main.js?v=3"></script>
</body>
</html>
