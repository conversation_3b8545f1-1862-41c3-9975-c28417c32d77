<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - IndianTutors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .chat-container { height: calc(100vh - 200px); }
        .messages-container { height: calc(100% - 80px); }
        .message-bubble {
            max-width: 70%;
            word-wrap: break-word;
        }
        .message-sent {
            background: linear-gradient(135deg, #00C2B3 0%, #00A89B 100%);
        }
        .message-received {
            background: #f1f5f9;
            color: #334155;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <a href="#" class="text-2xl font-bold" style="color: #00C2B3;">IndianTutors</a>
                    <button onclick="goBack()" class="text-gray-600 transition-colors" style="color: #5A5A5A;" onmouseover="this.style.color='#00C2B3'" onmouseout="this.style.color='#5A5A5A'">
                        ← Back to Dashboard
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userEmail" class="text-gray-600">Loading...</span>
                    <button onclick="handleLogout()" class="text-red-600 hover:text-red-700">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="flex chat-container" style="height: 600px;">
                <!-- Chat List Sidebar -->
                <div class="w-1/3 border-r border-gray-200 bg-gray-50">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Messages</h2>
                    </div>
                    <div id="chatsList" class="overflow-y-auto h-full">
                        <!-- Sample conversations -->
                        <div class="chat-item p-4 border-b border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors border-gray-200" style="background-color: rgba(0, 194, 179, 0.1);" onclick="openChat('1', 'Rajesh Kumar')">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: rgba(0, 194, 179, 0.2);">
                                    <span class="font-semibold" style="color: #00C2B3;">R</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-semibold text-gray-900 truncate">Rajesh Kumar</h3>
                                        <span class="text-xs text-gray-500">2 min ago</span>
                                    </div>
                                    <p class="text-sm text-gray-500 truncate">
                                        Thank you for the lesson today!
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="chat-item p-4 border-b border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors" onclick="openChat('2', 'Priya Sharma')">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: rgba(0, 194, 179, 0.2);">
                                    <span class="font-semibold" style="color: #00C2B3;">P</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-semibold text-gray-900 truncate">Priya Sharma</h3>
                                        <span class="text-xs text-gray-500">1 hour ago</span>
                                    </div>
                                    <p class="text-sm text-gray-500 truncate">
                                        Can we reschedule tomorrow's lesson?
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="chat-item p-4 border-b border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors" onclick="openChat('3', 'Amit Patel')">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 font-semibold">A</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex justify-between items-start">
                                        <h3 class="font-semibold text-gray-900 truncate">Amit Patel</h3>
                                        <span class="text-xs text-gray-500">Yesterday</span>
                                    </div>
                                    <p class="text-sm text-gray-500 truncate">
                                        Looking forward to our next session
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages Area -->
                <div class="flex-1 flex flex-col">
                    <div id="chatHeader" class="p-4 border-b border-gray-200 bg-white">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center" style="background-color: rgba(0, 194, 179, 0.2);">
                                <span class="font-semibold" style="color: #00C2B3;" id="chatUserInitial">R</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900" id="chatUserName">Rajesh Kumar</h3>
                                <p class="text-sm text-gray-500">Student</p>
                            </div>
                        </div>
                    </div>

                    <div id="messagesContainer" class="flex-1 overflow-y-auto p-4 messages-container">
                        <div id="messagesList" class="space-y-4">
                            <!-- Sample messages for Rajesh Kumar -->
                            <div class="flex justify-start">
                                <div class="message-bubble message-received px-4 py-2 rounded-lg">
                                    <p class="text-sm">Hi! I'm looking forward to our Hindi lesson tomorrow.</p>
                                    <p class="text-xs opacity-75 mt-1">10:30 AM</p>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <div class="message-bubble message-sent text-white px-4 py-2 rounded-lg">
                                    <p class="text-sm">Great! I've prepared some new vocabulary for you.</p>
                                    <p class="text-xs opacity-75 mt-1">10:32 AM</p>
                                </div>
                            </div>
                            
                            <div class="flex justify-start">
                                <div class="message-bubble message-received px-4 py-2 rounded-lg">
                                    <p class="text-sm">Perfect! Should I bring my notebook?</p>
                                    <p class="text-xs opacity-75 mt-1">10:35 AM</p>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <div class="message-bubble message-sent text-white px-4 py-2 rounded-lg">
                                    <p class="text-sm">Yes, definitely! We'll be doing some writing exercises.</p>
                                    <p class="text-xs opacity-75 mt-1">10:36 AM</p>
                                </div>
                            </div>
                            
                            <div class="flex justify-start">
                                <div class="message-bubble message-received px-4 py-2 rounded-lg">
                                    <p class="text-sm">Thank you for the lesson today!</p>
                                    <p class="text-xs opacity-75 mt-1">2 min ago</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="messageInput" class="p-4 border-t border-gray-200 bg-white">
                        <div class="flex space-x-3">
                            <input 
                                type="text" 
                                id="messageText" 
                                placeholder="Type your message..." 
                                class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:border-transparent"
                                style="--tw-ring-color: #00C2B3;"
                                onkeypress="handleMessageKeyPress(event)"
                            >
                            <button 
                                onclick="sendMessage()" 
                                class="text-white px-6 py-2 rounded-lg transition-colors font-medium"
                                style="background-color: #00C2B3;"
                                onmouseover="this.style.backgroundColor='#00A89B'"
                                onmouseout="this.style.backgroundColor='#00C2B3'"
                            >
                                Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Simple Messaging Service -->
    <script src="simple-messaging.js"></script>

    <script>
        // Supabase Configuration
        const SUPABASE_URL = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';

        let supabase;
        let messaging;
        let currentChatId = null;
        let allChats = [];

        // Initialize the application
        async function initializeApp() {
            try {
                // Initialize Supabase
                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

                // Check authentication
                const { data: { session } } = await supabase.auth.getSession();
                if (!session) {
                    window.location.href = 'index.html';
                    return;
                }

                // Initialize messaging
                messaging = new SimpleMessaging(supabase);
                await messaging.initialize();

                // Update user info
                document.getElementById('userEmail').textContent = session.user.email;

                // Load chats
                await loadChats();

            } catch (error) {
                console.error('Error initializing app:', error);
                alert('Failed to initialize messaging. Please refresh the page.');
            }
        }

        // Load user's chats
        async function loadChats() {
            try {
                allChats = await messaging.getUserChats();
                displayChatsList();

                // Open first chat if available
                if (allChats.length > 0) {
                    await openChat(allChats[0].id, allChats[0].otherUser.name);
                }
            } catch (error) {
                console.error('Error loading chats:', error);
                document.getElementById('chatsList').innerHTML = '<div class="p-4 text-center text-gray-500">Failed to load chats</div>';
            }
        }

        // Display chats list
        function displayChatsList() {
            const chatsList = document.getElementById('chatsList');

            if (allChats.length === 0) {
                chatsList.innerHTML = '<div class="p-4 text-center text-gray-500">No conversations yet</div>';
                return;
            }

            const chatsHTML = allChats.map(chat => {
                const userName = chat.otherUser.name;
                const userInitial = userName.charAt(0).toUpperCase();
                const lastMessage = chat.latestMessage ? chat.latestMessage.content : 'No messages yet';
                const timeAgo = chat.latestMessage ? formatTimeAgo(new Date(chat.latestMessage.created_at)) : '';

                return `
                    <div class="chat-item p-4 border-b border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors"
                         onclick="openChat('${chat.id}', '${userName}')">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: rgba(0, 194, 179, 0.2);">
                                <span class="font-semibold" style="color: #00C2B3;">${userInitial}</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex justify-between items-start">
                                    <h3 class="font-semibold text-gray-900 truncate">${userName}</h3>
                                    <span class="text-xs text-gray-500">${timeAgo}</span>
                                </div>
                                <p class="text-sm text-gray-500 truncate">${lastMessage}</p>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            chatsList.innerHTML = chatsHTML;
        }

        // Open a specific chat
        async function openChat(chatId, userName) {
            try {
                currentChatId = chatId;

                // Update chat header
                document.getElementById('chatUserName').textContent = userName;
                document.getElementById('chatUserInitial').textContent = userName.charAt(0).toUpperCase();

                // Load and display messages
                const messages = await messaging.getChatMessages(chatId);
                displayMessages(messages);

                // Highlight selected chat
                document.querySelectorAll('.chat-item').forEach(item => {
                    item.classList.remove('border-gray-200');
                    item.style.backgroundColor = '';
                    item.style.borderColor = '';
                });

                if (event && event.currentTarget) {
                    event.currentTarget.style.backgroundColor = 'rgba(0, 194, 179, 0.1)';
                    event.currentTarget.style.borderColor = 'rgba(0, 194, 179, 0.3)';
                }

                // Subscribe to real-time updates
                messaging.unsubscribeFromChat();
                messaging.subscribeToChat(chatId, handleNewMessage);

            } catch (error) {
                console.error('Error opening chat:', error);
                alert('Failed to load chat messages');
            }
        }

        // Display messages in the chat
        function displayMessages(messages) {
            const messagesList = document.getElementById('messagesList');

            const messagesHTML = messages.map(message => {
                const isSent = message.sender_id === messaging.currentUser.id;
                const messageClass = isSent ? 'message-sent text-white ml-auto' : 'message-received mr-auto';
                const justifyClass = isSent ? 'justify-end' : 'justify-start';
                const time = formatTime(new Date(message.created_at));

                return `
                    <div class="flex ${justifyClass}">
                        <div class="message-bubble ${messageClass} px-4 py-2 rounded-lg">
                            <p class="text-sm">${escapeHtml(message.content)}</p>
                            <p class="text-xs opacity-75 mt-1">${time}</p>
                        </div>
                    </div>
                `;
            }).join('');

            messagesList.innerHTML = messagesHTML;
            scrollToBottom();
        }

        // Handle new message from real-time subscription
        function handleNewMessage(message) {
            if (message.chat_id === currentChatId) {
                const messagesList = document.getElementById('messagesList');
                const isSent = message.sender_id === messaging.currentUser.id;
                const messageClass = isSent ? 'message-sent text-white ml-auto' : 'message-received mr-auto';
                const justifyClass = isSent ? 'justify-end' : 'justify-start';
                const time = formatTime(new Date(message.created_at));

                const messageHTML = `
                    <div class="flex ${justifyClass}">
                        <div class="message-bubble ${messageClass} px-4 py-2 rounded-lg">
                            <p class="text-sm">${escapeHtml(message.content)}</p>
                            <p class="text-xs opacity-75 mt-1">${time}</p>
                        </div>
                    </div>
                `;

                messagesList.insertAdjacentHTML('beforeend', messageHTML);
                scrollToBottom();
            }

            // Refresh chat list to show latest message
            loadChats();
        }

        // Send a message
        async function sendMessage() {
            const messageInput = document.getElementById('messageText');
            const content = messageInput.value.trim();

            if (!content || !currentChatId) return;

            try {
                await messaging.sendMessage(currentChatId, content);
                messageInput.value = '';
            } catch (error) {
                console.error('Error sending message:', error);
                alert('Failed to send message. Please try again.');
            }
        }

        function handleMessageKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }

        function scrollToBottom() {
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function goBack() {
            // Detect if user came from student or tutor dashboard
            const referrer = document.referrer;
            if (referrer.includes('tutor-dashboard')) {
                window.location.href = 'tutor-dashboard.html';
            } else {
                window.location.href = 'student-dashboard.html';
            }
        }

        function handleLogout() {
            if (confirm('Are you sure you want to log out?')) {
                window.location.href = 'index.html';
            }
        }

        // Utility functions
        function formatTime(date) {
            return date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        function formatTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return 'now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            return date.toLocaleDateString();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', initializeApp);

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (messaging) {
                messaging.unsubscribeFromChat();
            }
        });
    </script>
</body>
</html>
