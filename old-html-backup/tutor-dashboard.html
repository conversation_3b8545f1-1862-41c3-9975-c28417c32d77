<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - IndianTutors</title>
    <meta name="description" content="Manage your teaching profile and students">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Responsive Global CSS with Teal Professional Theme -->
    <link rel="stylesheet" href="responsive-global.css?v=12&t=20250614-critical-fix">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-inter">
    <!-- Navigation Header -->
    <nav class="responsive-nav bg-white shadow-sm border-b border-gray-200">
        <div class="responsive-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="nav-content flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <h1 class="text-2xl font-bold text-gray-900">IndianTutors</h1>
                </div>

                <!-- Center Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Teacher Settings
                    </a>
                    <a href="tutor-calendar.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0V7a4 4 0 118 0v6"></path>
                        </svg>
                        My Calendar
                    </a>
                    <a href="tutor-messages.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200 relative">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Messages
                        <span id="tutorUnreadBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                    </a>
                </div>

                <!-- Profile Dropdown -->
                <div class="relative">
                    <button id="profileBtn" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <img id="profileAvatar" src="https://via.placeholder.com/40" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                        <span id="profileName" class="hidden md:block text-sm font-medium text-gray-700">Loading...</span>
                        <svg class="w-4 h-4 text-gray-400 transition-transform duration-200" id="dropdownArrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="dropdownMenu" class="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 opacity-0 invisible transform scale-95 transition-all duration-200 ease-in-out">
                        <!-- Profile Header -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center space-x-3">
                                <img id="dropdownAvatar" src="https://via.placeholder.com/40" alt="Profile" class="w-10 h-10 rounded-full object-cover">
                                <div>
                                    <div id="dropdownName" class="text-sm font-medium text-gray-900">Loading...</div>
                                    <div id="dropdownEmail" class="text-xs text-gray-500">Loading...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="py-2">
                            <a href="tutor-lessons.html" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                My Lessons
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                My Students
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                My Wallet
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                My Teacher Profile
                            </a>
                            <div class="border-t border-gray-100 my-2"></div>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Account Settings
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                </svg>
                                Support
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Upgrade to Professional
                            </a>
                            <div class="border-t border-gray-100 my-2"></div>
                            <a href="home.html" class="flex items-center px-4 py-2 text-sm text-green-600 hover:bg-green-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                🎓 Switch to Student Mode
                            </a>
                            <button onclick="handleLogout()" class="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Log Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Dashboard Content -->
    <main class="responsive-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div class="responsive-grid grid-1 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- Left Column - Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Teacher Profile Card -->
                <div class="card card-body bg-white rounded-lg shadow-lg p-4 sm:p-6">
                    <div class="flex items-center space-x-4 mb-6">
                        <img id="teacherAvatar" src="https://via.placeholder.com/80" alt="Teacher Avatar" class="w-20 h-20 rounded-full object-cover border-4 border-green-500">
                        <div>
                            <h2 id="teacherName" class="text-2xl font-bold text-gray-900">Loading...</h2>
                            <p id="teacherId" class="text-sm text-gray-500 mb-1">ID: Loading...</p>
                            <p id="teacherLanguages" class="text-sm font-medium text-green-600">Loading...</p>
                            <p class="text-sm text-gray-500">UTC+05:30 (Asia/Kolkata)</p>
                        </div>
                    </div>

                    <div class="responsive-grid grid-1 sm:grid-3 gap-3 sm:gap-4">
                        <div class="text-center p-4 bg-gray-50 rounded-lg border">
                            <div id="upcomingLessons" class="text-2xl font-bold text-gray-900">0</div>
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Upcoming Lessons</div>
                        </div>
                        <div id="actionRequiredCard" class="text-center p-4 bg-gray-50 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer" onclick="window.location.href='lesson-requests.html'">
                            <div id="actionRequired" class="text-2xl font-bold text-gray-900">0</div>
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Action Required</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg border">
                            <div id="packageAction" class="text-2xl font-bold text-gray-900">0</div>
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Package Action</div>
                        </div>
                    </div>
                </div>

                <!-- Earnings Card -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex justify-between items-start mb-6">
                        <h3 class="flex items-center text-lg font-semibold text-gray-900">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Earnings
                        </h3>
                        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            Withdraw
                        </button>
                    </div>

                    <div class="text-3xl font-bold text-gray-900 mb-1">$ 0.00 <span class="text-base font-normal">USD</span></div>
                    <p class="text-sm text-gray-500 mb-6">Total Balance</p>

                    <div class="grid grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">$ 3.95</h4>
                            <p class="text-sm text-gray-500">May Earnings</p>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">$ 0.00</h4>
                            <p class="text-sm text-gray-500">May Reward</p>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <p class="text-sm font-semibold text-green-900 mb-1">✅ You can do it!</p>
                        <p class="text-sm text-green-800">Check out these tips to help you get an excellence reward next month!</p>
                    </div>
                </div>

                <!-- Horizontal Action Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Instant Lesson Card -->
                    <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Instant Lesson</h3>
                        <p class="text-sm text-gray-600 mb-4">Automatically accept and start a lesson 5 minutes after receiving an Instant Lesson request.</p>
                        <button class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            Enable
                        </button>
                    </div>

                    <!-- Teacher Forum Card -->
                    <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                        <div class="flex items-center mb-3">
                            <div class="text-2xl mr-3">👥</div>
                            <h3 class="text-lg font-semibold text-gray-900">Teacher Forum</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Where great minds meet</p>
                        <a href="#" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 inline-block text-center">
                            Visit Forum
                        </a>
                    </div>

                    <!-- Teacher Tools Card -->
                    <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                        <div class="flex items-center mb-3">
                            <div class="text-2xl mr-3">🛠️</div>
                            <h3 class="text-lg font-semibold text-gray-900">Teacher Tools</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Create learning content to help your students improve</p>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 text-center cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                                <div class="text-lg mb-1">📝</div>
                                <div class="text-xs text-gray-600 font-medium">Quiz</div>
                            </div>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 text-center cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                                <div class="text-lg mb-1">📖</div>
                                <div class="text-xs text-gray-600 font-medium">Vocabulary</div>
                            </div>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 text-center cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                                <div class="text-lg mb-1">🎧</div>
                                <div class="text-xs text-gray-600 font-medium">Podcast</div>
                            </div>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 text-center cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                                <div class="text-lg mb-1">🏆</div>
                                <div class="text-xs text-gray-600 font-medium">Certificate</div>
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Knowledge Base Card -->
                    <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                        <div class="flex items-center mb-3">
                            <div class="text-2xl mr-3">📚</div>
                            <h3 class="text-lg font-semibold text-gray-900">Knowledge Base</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Unleash the secret of success</p>
                        <a href="#" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 inline-block text-center">
                            Learn More
                        </a>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Opportunities Section -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Opportunities</h3>
                    <p class="text-sm text-gray-600 mb-4">Invite them to book another lesson</p>

                    <div class="bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded uppercase tracking-wide mb-3 inline-block">
                        REGULAR STUDENT CATCH-UP
                    </div>

                    <div class="flex items-center space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <img src="https://via.placeholder.com/40" alt="Student" class="w-10 h-10 rounded-full object-cover">
                        <div class="flex-1">
                            <h4 class="text-sm font-semibold text-gray-900">Shyam Syangtan</h4>
                            <p class="text-xs text-gray-500">105 Lessons</p>
                        </div>
                        <div class="text-right">
                            <p class="text-xs font-semibold text-gray-900">Last Lesson</p>
                            <p class="text-xs text-gray-500">5 days ago</p>
                            <p class="text-xs font-semibold text-gray-900 mt-1">Frequency</p>
                            <p class="text-xs text-gray-500">Occasional</p>
                        </div>
                    </div>
                </div>

                <!-- Mobile Navigation Menu (Hidden on Desktop) -->
                <div class="md:hidden bg-white rounded-lg shadow-lg p-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Access</h3>
                    <div class="space-y-2">
                        <a href="#" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Teacher Settings
                        </a>
                        <a href="tutor-calendar.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0V7a4 4 0 118 0v6"></path>
                            </svg>
                            My Calendar
                        </a>
                        <a href="tutor-messages.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Messages
                            <span id="tutorUnreadBadgeDropdown" class="ml-auto bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* Action Required Card Notification Styling */
        #actionRequiredCard.has-notification {
            background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
            border-color: #f59e0b;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        #actionRequiredCard.has-notification #actionRequired {
            color: #92400e;
        }

        #actionRequiredCard.has-notification .text-gray-500 {
            color: #92400e !important;
        }

        /* Pulse animation for notification */
        #actionRequiredCard.has-notification {
            animation: pulse-notification 2s infinite;
        }

        @keyframes pulse-notification {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
    </style>

    <script>
        // Dropdown functionality
        const profileBtn = document.getElementById('profileBtn');
        const dropdownMenu = document.getElementById('dropdownMenu');
        const dropdownArrow = document.getElementById('dropdownArrow');
        let isDropdownOpen = false;

        function toggleDropdown() {
            isDropdownOpen = !isDropdownOpen;

            if (isDropdownOpen) {
                dropdownMenu.classList.remove('opacity-0', 'invisible', 'scale-95');
                dropdownMenu.classList.add('opacity-100', 'visible', 'scale-100');
                dropdownArrow.style.transform = 'rotate(180deg)';
            } else {
                dropdownMenu.classList.add('opacity-0', 'invisible', 'scale-95');
                dropdownMenu.classList.remove('opacity-100', 'visible', 'scale-100');
                dropdownArrow.style.transform = 'rotate(0deg)';
            }
        }

        // Toggle dropdown on button click
        profileBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdownMenu.contains(e.target) && !profileBtn.contains(e.target)) {
                if (isDropdownOpen) {
                    toggleDropdown();
                }
            }
        });

        // Prevent dropdown from closing when clicking inside it
        dropdownMenu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to log out?')) {
                // Clear any stored auth data
                localStorage.removeItem('supabase.auth.token');
                sessionStorage.clear();

                // Redirect to home page
                window.location.href = 'index.html';
            }
        }

        // Load user data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
        });

        async function loadUserProfile() {
            try {
                // This would typically load from Supabase
                // For now, using placeholder data
                const userData = {
                    name: 'Sai Mai',
                    email: '<EMAIL>',
                    avatar: 'https://via.placeholder.com/40',
                    id: 'T12345',
                    languages: 'Hindi, English, Tamil'
                };

                // Update profile elements
                document.getElementById('profileName').textContent = userData.name;
                document.getElementById('profileAvatar').src = userData.avatar;
                document.getElementById('dropdownName').textContent = userData.name;
                document.getElementById('dropdownEmail').textContent = userData.email;
                document.getElementById('dropdownAvatar').src = userData.avatar;

                // Update teacher profile
                document.getElementById('teacherName').textContent = userData.name;
                document.getElementById('teacherId').textContent = `ID: ${userData.id}`;
                document.getElementById('teacherLanguages').textContent = userData.languages;
                document.getElementById('teacherAvatar').src = userData.avatar;

            } catch (error) {
                console.error('Error loading user profile:', error);
            }
        }
    </script>

    <!-- Database Error Notification System -->
    <script src="database-error-notification.js?v=1"></script>

    <script src="tutor-dashboard.js?v=6"></script>
</body>
</html>
