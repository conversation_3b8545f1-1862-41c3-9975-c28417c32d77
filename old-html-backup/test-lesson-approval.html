<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Lesson Approval System</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">🧪 Test Lesson Approval System</h1>
        
        <!-- Test Results -->
        <div id="testResults" class="space-y-4 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Test Results</h2>
                <div id="results" class="space-y-2">
                    <div class="text-gray-600">Running tests...</div>
                </div>
            </div>
        </div>

        <!-- Manual Test Actions -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Manual Test Actions</h2>
            <div class="grid grid-cols-2 gap-4">
                <button onclick="testDatabaseConnection()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Test Database Connection
                </button>
                <button onclick="createTestRequest()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    🧪 Test Complete Workflow
                </button>
                <button onclick="checkTables()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    Check Tables
                </button>
                <button onclick="testRLSPolicies()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
                    Test RLS Policies
                </button>
                <button onclick="testStudentLessonFunctions()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                    Test Student Functions
                </button>
                <button onclick="fixMissingLessons()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    Fix Missing Lessons
                </button>
            </div>
        </div>

        <!-- Login Section -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4">Authentication</h2>
            <div id="authStatus" class="mb-4">
                <div class="text-gray-600">Checking authentication...</div>
            </div>
            <button onclick="loginWithGoogle()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                Login with Google
            </button>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        // Supabase Configuration
        const SUPABASE_URL = 'https://qbyyutebrgpxngvwenkd.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY';
        
        let supabase;
        let currentUser;

        // Initialize
        document.addEventListener('DOMContentLoaded', async function() {
            supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            await checkAuth();
            await runTests();
        });

        async function checkAuth() {
            try {
                const { data: { session } } = await supabase.auth.getSession();
                const authStatus = document.getElementById('authStatus');
                
                if (session) {
                    currentUser = session.user;
                    authStatus.innerHTML = `
                        <div class="text-green-600">✅ Authenticated as: ${currentUser.email}</div>
                        <div class="text-sm text-gray-600">User ID: ${currentUser.id}</div>
                    `;
                } else {
                    authStatus.innerHTML = '<div class="text-red-600">❌ Not authenticated</div>';
                }
            } catch (error) {
                document.getElementById('authStatus').innerHTML = `<div class="text-red-600">❌ Auth error: ${error.message}</div>`;
            }
        }

        async function runTests() {
            const results = document.getElementById('results');
            results.innerHTML = '';

            const tests = [
                { name: 'Database Connection', fn: testDatabaseConnection },
                { name: 'Tables Existence', fn: checkTables },
                { name: 'RLS Policies', fn: testRLSPolicies }
            ];

            for (const test of tests) {
                try {
                    const result = await test.fn();
                    addResult(test.name, result, 'success');
                } catch (error) {
                    addResult(test.name, error.message, 'error');
                }
            }
        }

        function addResult(testName, message, type) {
            const results = document.getElementById('results');
            const color = type === 'success' ? 'text-green-600' : 'text-red-600';
            const icon = type === 'success' ? '✅' : '❌';
            
            results.innerHTML += `
                <div class="${color}">
                    ${icon} <strong>${testName}:</strong> ${message}
                </div>
            `;
        }

        async function testDatabaseConnection() {
            const { data, error } = await supabase.from('users').select('count').limit(1);
            if (error) throw error;
            return 'Connected successfully';
        }

        async function checkTables() {
            const tables = ['lesson_requests', 'lessons', 'users', 'tutors'];
            const results = [];
            
            for (const table of tables) {
                try {
                    const { data, error } = await supabase.from(table).select('count').limit(1);
                    if (error) {
                        results.push(`${table}: ❌ ${error.message}`);
                    } else {
                        results.push(`${table}: ✅ Exists`);
                    }
                } catch (e) {
                    results.push(`${table}: ❌ ${e.message}`);
                }
            }
            
            return results.join(', ');
        }

        async function testRLSPolicies() {
            if (!currentUser) {
                throw new Error('Must be authenticated to test RLS');
            }
            
            try {
                const { data, error } = await supabase
                    .from('lesson_requests')
                    .select('*')
                    .limit(1);
                    
                if (error) {
                    return `RLS Error: ${error.message}`;
                }
                
                return 'RLS policies working';
            } catch (error) {
                return `RLS Test Failed: ${error.message}`;
            }
        }

        async function createTestRequest() {
            if (!currentUser) {
                alert('Please login first');
                return;
            }

            try {
                const { data, error } = await supabase
                    .from('lesson_requests')
                    .insert({
                        tutor_id: currentUser.id, // Using same user as both tutor and student for testing
                        student_id: currentUser.id,
                        requested_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Tomorrow
                        requested_start_time: '14:00:00',
                        requested_end_time: '15:00:00',
                        student_message: 'Test lesson request created by test script'
                    })
                    .select()
                    .single();

                if (error) throw error;

                addResult('Create Test Request', `Created request ID: ${data.id}`, 'success');

                // Now test the approval workflow
                await testApprovalWorkflow(data.id);

            } catch (error) {
                addResult('Create Test Request', error.message, 'error');
            }
        }

        async function testApprovalWorkflow(requestId) {
            try {
                addResult('Testing Approval Workflow', 'Starting approval test...', 'success');

                // Step 1: Approve the request
                const { data: approvedRequest, error: approveError } = await supabase
                    .from('lesson_requests')
                    .update({
                        status: 'approved',
                        tutor_response: 'Test approval',
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', requestId)
                    .select()
                    .single();

                if (approveError) throw approveError;

                addResult('Approve Request', `Request ${requestId} approved successfully`, 'success');

                // Step 2: Wait a moment for trigger to fire
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Step 3: Check if lesson was created
                const { data: lessons, error: lessonError } = await supabase
                    .from('lessons')
                    .select('*')
                    .eq('student_id', currentUser.id)
                    .eq('tutor_id', currentUser.id)
                    .eq('lesson_date', approvedRequest.requested_date)
                    .eq('start_time', approvedRequest.requested_start_time);

                if (lessonError) throw lessonError;

                if (lessons && lessons.length > 0) {
                    addResult('Lesson Creation Check', `✅ Lesson created successfully! ID: ${lessons[0].id}`, 'success');

                    // Step 4: Test student lesson loading function
                    await testStudentLessonFunctions();
                } else {
                    addResult('Lesson Creation Check', '❌ No lesson found after approval', 'error');

                    // Try manual lesson creation
                    await testManualLessonCreation(approvedRequest);
                }

            } catch (error) {
                addResult('Approval Workflow Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testManualLessonCreation(request) {
            try {
                addResult('Manual Lesson Creation', 'Testing manual lesson creation...', 'success');

                const { data: lessonId, error } = await supabase
                    .rpc('manual_create_lesson', {
                        p_tutor_id: request.tutor_id,
                        p_student_id: request.student_id,
                        p_lesson_date: request.requested_date,
                        p_start_time: request.requested_start_time,
                        p_end_time: request.requested_end_time,
                        p_notes: 'Test lesson created manually'
                    });

                if (error) throw error;

                addResult('Manual Lesson Creation', `✅ Manual lesson created! ID: ${lessonId}`, 'success');
                await testStudentLessonFunctions();

            } catch (error) {
                addResult('Manual Lesson Creation', `Error: ${error.message}`, 'error');
            }
        }

        async function testStudentLessonFunctions() {
            try {
                addResult('Student Lesson Functions', 'Testing lesson loading functions...', 'success');

                // Test optimized function
                try {
                    const { data: optimizedLessons, error: optimizedError } = await supabase
                        .rpc('get_student_lessons_optimized', { student_user_id: currentUser.id });

                    if (optimizedError) throw optimizedError;

                    addResult('Optimized Function', `✅ Found ${optimizedLessons?.length || 0} lessons`, 'success');
                } catch (error) {
                    addResult('Optimized Function', `Error: ${error.message}`, 'error');
                }

                // Test basic function
                try {
                    const { data: basicLessons, error: basicError } = await supabase
                        .rpc('get_student_lessons', { student_user_id: currentUser.id });

                    if (basicError) throw basicError;

                    addResult('Basic Function', `✅ Found ${basicLessons?.length || 0} lessons`, 'success');
                } catch (error) {
                    addResult('Basic Function', `Error: ${error.message}`, 'error');
                }

                // Test direct query
                try {
                    const { data: directLessons, error: directError } = await supabase
                        .from('lessons')
                        .select('*')
                        .eq('student_id', currentUser.id);

                    if (directError) throw directError;

                    addResult('Direct Query', `✅ Found ${directLessons?.length || 0} lessons`, 'success');
                } catch (error) {
                    addResult('Direct Query', `Error: ${error.message}`, 'error');
                }

            } catch (error) {
                addResult('Student Lesson Functions', `Error: ${error.message}`, 'error');
            }
        }

        async function fixMissingLessons() {
            try {
                addResult('Fix Missing Lessons', 'Running fix for approved requests without lessons...', 'success');

                const { data, error } = await supabase.rpc('fix_missing_lessons');

                if (error) throw error;

                if (data && data.length > 0) {
                    data.forEach(result => {
                        addResult('Fix Result', `Request ${result.request_id}: ${result.message}`,
                                 result.lesson_created ? 'success' : 'error');
                    });
                } else {
                    addResult('Fix Missing Lessons', 'No approved requests found or all lessons already exist', 'success');
                }

            } catch (error) {
                addResult('Fix Missing Lessons', `Error: ${error.message}`, 'error');
            }
        }

        async function loginWithGoogle() {
            try {
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: window.location.href
                    }
                });

                if (error) throw error;
            } catch (error) {
                alert('Login failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
