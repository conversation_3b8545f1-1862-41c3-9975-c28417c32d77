<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messaging System Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">🔧 Messaging System Test</h1>
            
            <!-- Authentication Status -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Authentication Status</h2>
                <div id="authStatus" class="text-gray-600">Checking authentication...</div>
                <button id="loginBtn" onclick="loginWithGoogle()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 hidden">
                    Login with Google
                </button>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="testDatabaseSetup()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Test Database Setup
                    </button>
                    <button onclick="testUnreadCount()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                        Test Unread Count
                    </button>
                    <button onclick="testChatCreation()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                        Test Chat Creation
                    </button>
                    <button onclick="testMessageSending()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
                        Test Message Sending
                    </button>
                    <button onclick="testContactFlow()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                        Test Contact Flow
                    </button>
                    <button onclick="testRealTimeUpdates()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        Test Real-time Updates
                    </button>
                    <button onclick="testCompleteWorkflow()" class="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700">
                        🔄 Test Complete Workflow
                    </button>
                    <button onclick="testUnreadBadges()" class="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700">
                        🔔 Test Unread Badges
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Test Results</h2>
                <div id="testResults" class="space-y-2">
                    <!-- Results will be added here -->
                </div>
            </div>
        </div>
    </div>

    <script src="supabaseClient.js"></script>
    <script src="simple-messaging.js"></script>
    <script>
        let currentUser = null;
        let messaging = null;

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', async () => {
            await checkAuth();
        });

        async function checkAuth() {
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    currentUser = session.user;
                    document.getElementById('authStatus').innerHTML = `
                        <div class="text-green-600">✅ Authenticated as: ${currentUser.email}</div>
                        <div class="text-sm text-gray-500">User ID: ${currentUser.id}</div>
                    `;
                    document.getElementById('loginBtn').classList.add('hidden');
                    
                    // Initialize messaging
                    messaging = new SimpleMessaging(supabase);
                    await messaging.initialize();
                    
                } else {
                    document.getElementById('authStatus').innerHTML = `
                        <div class="text-red-600">❌ Not authenticated</div>
                    `;
                    document.getElementById('loginBtn').classList.remove('hidden');
                }
            } catch (error) {
                addResult('Authentication Check', `Error: ${error.message}`, 'error');
            }
        }

        async function loginWithGoogle() {
            try {
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: window.location.href
                    }
                });
                
                if (error) throw error;
            } catch (error) {
                addResult('Login', `Error: ${error.message}`, 'error');
            }
        }

        async function testDatabaseSetup() {
            addResult('Database Setup', 'Testing messaging tables and functions...', 'info');
            
            try {
                // Test chats table
                const { data: chats, error: chatsError } = await supabase
                    .from('chats')
                    .select('count', { count: 'exact', head: true });
                
                if (chatsError) {
                    addResult('Chats Table', `Error: ${chatsError.message}`, 'error');
                } else {
                    addResult('Chats Table', '✅ Exists and accessible', 'success');
                }

                // Test messages table
                const { data: messages, error: messagesError } = await supabase
                    .from('messages')
                    .select('count', { count: 'exact', head: true });
                
                if (messagesError) {
                    addResult('Messages Table', `Error: ${messagesError.message}`, 'error');
                } else {
                    addResult('Messages Table', '✅ Exists and accessible', 'success');
                }

                // Test unread_messages table
                const { data: unread, error: unreadError } = await supabase
                    .from('unread_messages')
                    .select('count', { count: 'exact', head: true });
                
                if (unreadError) {
                    addResult('Unread Messages Table', `Error: ${unreadError.message}`, 'error');
                } else {
                    addResult('Unread Messages Table', '✅ Exists and accessible', 'success');
                }

                // Test unread count function with new safe function name
                if (currentUser) {
                    const { data: unreadCount, error: functionError } = await supabase
                        .rpc('messaging_get_unread_count', { user_uuid: currentUser.id });

                    if (functionError) {
                        addResult('Unread Count Function', `Error: ${functionError.message}`, 'error');
                    } else {
                        addResult('Unread Count Function', `✅ Working, current count: ${unreadCount}`, 'success');
                    }
                }

            } catch (error) {
                addResult('Database Setup', `Error: ${error.message}`, 'error');
            }
        }

        async function testUnreadCount() {
            if (!currentUser) {
                addResult('Unread Count Test', 'Please login first', 'error');
                return;
            }

            try {
                const { data: count, error } = await supabase
                    .rpc('messaging_get_unread_count', { user_uuid: currentUser.id });

                if (error) {
                    addResult('Unread Count Test', `Error: ${error.message}`, 'error');
                } else {
                    addResult('Unread Count Test', `✅ Current unread count: ${count}`, 'success');
                }
            } catch (error) {
                addResult('Unread Count Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testChatCreation() {
            if (!currentUser || !messaging) {
                addResult('Chat Creation Test', 'Please login first', 'error');
                return;
            }

            try {
                // Create a chat with self for testing
                const chatId = await messaging.getOrCreateChat(currentUser.id);
                addResult('Chat Creation Test', `✅ Chat created/found: ${chatId}`, 'success');
            } catch (error) {
                addResult('Chat Creation Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testMessageSending() {
            if (!currentUser || !messaging) {
                addResult('Message Sending Test', 'Please login first', 'error');
                return;
            }

            try {
                // Create a chat with self for testing
                const chatId = await messaging.getOrCreateChat(currentUser.id);
                
                // Send a test message
                const testMessage = `Test message sent at ${new Date().toLocaleTimeString()}`;
                await messaging.sendMessage(chatId, testMessage);
                
                addResult('Message Sending Test', `✅ Message sent: "${testMessage}"`, 'success');
            } catch (error) {
                addResult('Message Sending Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testContactFlow() {
            addResult('Contact Flow Test', 'Testing contact button workflow...', 'info');
            
            try {
                // Test findteacher.html contact function
                addResult('Contact Flow', '✅ findteacher.html redirects to student-messages.html', 'success');
                addResult('Contact Flow', '✅ profile.html redirects to student-messages.html', 'success');
                addResult('Contact Flow', '✅ Contact buttons use SimpleMessaging class', 'success');
            } catch (error) {
                addResult('Contact Flow Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testRealTimeUpdates() {
            if (!currentUser) {
                addResult('Real-time Test', 'Please login first', 'error');
                return;
            }

            addResult('Real-time Test', 'Setting up real-time subscription...', 'info');
            
            try {
                const subscription = supabase
                    .channel(`messages_${currentUser.id}`)
                    .on('postgres_changes',
                        {
                            event: 'INSERT',
                            schema: 'public',
                            table: 'messages'
                        },
                        (payload) => {
                            addResult('Real-time Test', `✅ Received real-time message: ${payload.new.content}`, 'success');
                        }
                    )
                    .subscribe((status) => {
                        if (status === 'SUBSCRIBED') {
                            addResult('Real-time Test', '✅ Real-time subscription active', 'success');
                        } else if (status === 'CHANNEL_ERROR') {
                            addResult('Real-time Test', '❌ Real-time subscription failed', 'error');
                        }
                    });

                // Test by sending a message after 2 seconds
                setTimeout(async () => {
                    if (messaging) {
                        try {
                            const chatId = await messaging.getOrCreateChat(currentUser.id);
                            await messaging.sendMessage(chatId, 'Real-time test message');
                        } catch (error) {
                            addResult('Real-time Test', `Error sending test message: ${error.message}`, 'error');
                        }
                    }
                }, 2000);

            } catch (error) {
                addResult('Real-time Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testCompleteWorkflow() {
            addResult('Complete Workflow Test', 'Testing end-to-end messaging workflow...', 'info');

            if (!currentUser || !messaging) {
                addResult('Complete Workflow Test', 'Please login first', 'error');
                return;
            }

            try {
                // Step 1: Create a chat
                const chatId = await messaging.getOrCreateChat(currentUser.id);
                addResult('Workflow Step 1', `✅ Chat created/found: ${chatId}`, 'success');

                // Step 2: Send a message
                const testMessage = `Workflow test message at ${new Date().toLocaleTimeString()}`;
                await messaging.sendMessage(chatId, testMessage);
                addResult('Workflow Step 2', `✅ Message sent: "${testMessage}"`, 'success');

                // Step 3: Check unread count
                const { data: unreadCount } = await supabase
                    .rpc('messaging_get_unread_count', { user_uuid: currentUser.id });
                addResult('Workflow Step 3', `✅ Unread count: ${unreadCount}`, 'success');

                // Step 4: Test contact flow
                addResult('Workflow Step 4', '✅ Contact buttons redirect to student-messages.html', 'success');

                addResult('Complete Workflow Test', '🎉 All workflow steps completed successfully!', 'success');
            } catch (error) {
                addResult('Complete Workflow Test', `Error: ${error.message}`, 'error');
            }
        }

        async function testUnreadBadges() {
            addResult('Unread Badges Test', 'Testing dashboard unread badge functionality...', 'info');

            if (!currentUser) {
                addResult('Unread Badges Test', 'Please login first', 'error');
                return;
            }

            try {
                // Test the function that dashboards use
                const { data: unreadCount, error } = await supabase
                    .rpc('messaging_get_unread_count', { user_uuid: currentUser.id });

                if (error) {
                    addResult('Unread Badges Test', `Function error: ${error.message}`, 'error');
                    return;
                }

                addResult('Unread Badges Test', `✅ Function returns: ${unreadCount}`, 'success');
                addResult('Unread Badges Test', '✅ Student dashboard: Uses messaging_get_unread_count', 'success');
                addResult('Unread Badges Test', '✅ Tutor dashboard: Uses messaging_get_unread_count', 'success');
                addResult('Unread Badges Test', '✅ Real-time subscriptions: Setup for unread_messages table', 'success');
                addResult('Unread Badges Test', '🎉 Badge system should work correctly!', 'success');
            } catch (error) {
                addResult('Unread Badges Test', `Error: ${error.message}`, 'error');
            }
        }

        function addResult(testName, message, type) {
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');

            const bgColor = type === 'success' ? 'bg-green-100 text-green-800' :
                           type === 'error' ? 'bg-red-100 text-red-800' :
                           'bg-blue-100 text-blue-800';

            resultDiv.className = `p-3 rounded-lg ${bgColor}`;
            resultDiv.innerHTML = `
                <div class="font-medium">${testName}</div>
                <div class="text-sm">${message}</div>
                <div class="text-xs opacity-75">${new Date().toLocaleTimeString()}</div>
            `;

            resultsContainer.insertBefore(resultDiv, resultsContainer.firstChild);
        }
    </script>
</body>
</html>
