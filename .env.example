# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://qbyyutebrgpxngvwenkd.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY

# Supabase Service Role Key (for server-side operations)
# Get this from Supabase Dashboard → Settings → API → service_role key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Next.js Configuration
# Replace with your actual Vercel domain
NEXTAUTH_URL=https://your-vercel-domain.vercel.app
NEXTAUTH_SECRET=your_nextauth_secret_here

# Google OAuth Configuration (if using Google login)
# Get these from Google Cloud Console → APIs & Services → Credentials
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Optional: Database URL for direct connections (if needed)
DATABASE_URL=postgresql://postgres:[password]@db.qbyyutebrgpxngvwenkd.supabase.co:5432/postgres

# Optional: Analytics and Monitoring
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
