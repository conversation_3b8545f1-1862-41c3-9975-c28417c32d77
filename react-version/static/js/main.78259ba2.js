/*! For license information please see main.78259ba2.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),a=n(43),o=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(l(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var h=Object.assign,f=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),k=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),j=Symbol.for("react.memo"),E=Symbol.for("react.lazy");Symbol.for("react.scope");var N=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var C=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var T=Symbol.iterator;function P(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=T&&e[T]||e["@@iterator"])?e:null}var O=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===O?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case S:return"Suspense";case _:return"SuspenseList";case N:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case k:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case x:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case j:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case E:t=e._payload,e=e._init;try{return R(e(t))}catch(n){}}return null}var L=Array.isArray,A=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z={pending:!1,data:null,method:null,action:null},D=[],M=-1;function U(e){return{current:e}}function F(e){0>M||(e.current=D[M],D[M]=null,M--)}function B(e,t){M++,D[M]=e.current,e.current=t}var H=U(null),q=U(null),W=U(null),V=U(null);function $(e,t){switch(B(W,t),B(q,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(H),B(H,e)}function K(){F(H),F(q),F(W)}function G(e){null!==e.memoizedState&&B(V,e);var t=H.current,n=od(t,e.type);t!==n&&(B(q,e),B(H,n))}function J(e){q.current===e&&(F(H),F(q)),V.current===e&&(F(V),Gd._currentValue=z)}var Q=Object.prototype.hasOwnProperty,Y=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,oe=r.unstable_NormalPriority,ie=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function he(e){if("function"===typeof le&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var fe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(pe(e)/me|0)|0},pe=Math.log,me=Math.LN2;var ge=256,ve=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,o=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~o)?a=ye(r):0!==(i&=s)?a=ye(i):n||0!==(n=s&~e)&&(a=ye(n)):0!==(s=r&~o)?a=ye(s):0!==i?a=ye(i):n||0!==(n=r&~e)&&(a=ye(n)),0===a?0:0!==t&&t!==a&&0===(t&o)&&((o=a&-a)>=(n=t&-t)||32===o&&0!==(4194048&n))?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function xe(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function _e(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function je(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ee(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-fe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ne(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-fe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Ce(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Te(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Pe(){var e=I.p;return 0!==e?e:void 0===(e=window.event)?32:ch(e.type)}var Oe=Math.random().toString(36).slice(2),Re="__reactFiber$"+Oe,Le="__reactProps$"+Oe,Ae="__reactContainer$"+Oe,Ie="__reactEvents$"+Oe,ze="__reactListeners$"+Oe,De="__reactHandles$"+Oe,Me="__reactResources$"+Oe,Ue="__reactMarker$"+Oe;function Fe(e){delete e[Re],delete e[Le],delete e[Ie],delete e[ze],delete e[De]}function Be(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Re])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Re]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function We(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Ue]=!0}var $e=new Set,Ke={};function Ge(e,t){Je(e,t),Je(e+"Capture",t)}function Je(e,t){for(Ke[e]=t,e=0;e<t.length;e++)$e.add(t[e])}var Qe,Ye,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,Q.call(et,a)||!Q.call(Ze,a)&&(Xe.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Qe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Qe=t&&t[1]||"",Ye=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Qe+e+Ye}var ot=!1;function it(e,t){if(!e||ot)return"";ot=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(o){r=o}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch((function(){}))}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=r.DetermineComponentFrameRoot(),i=o[0],s=o[1];if(i&&s){var l=i.split("\n"),c=s.split("\n");for(a=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===l.length||a===c.length)for(r=l.length-1,a=c.length-1;1<=r&&0<=a&&l[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(l[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||l[r]!==c[a]){var u="\n"+l[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{ot=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function st(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function lt(e){try{var t="";do{t+=st(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ht(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function ft(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function mt(e){return e.replace(pt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function gt(e,t,n,r,a,o,i,s){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?yt(e,i,ct(t)):null!=n?yt(e,i,ct(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=o&&(e.defaultChecked=!!o),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+ct(s):e.removeAttribute("name")}function vt(e,t,n,r,a,o,i,s){if(null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.type=o),null!=t||null!=n){if(!("submit"!==o&&"reset"!==o||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,s||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function yt(e,t,n){"number"===t&&ft(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(L(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function xt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function _t(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function jt(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&_t(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&_t(e,o,t[o])}function Et(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ct=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tt(e){return Ct.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Pt=null;function Ot(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,Lt=null;function At(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Le]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Le]||null;if(!a)throw Error(i(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ht(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var It=!1;function zt(e,t,n){if(It)return e(t,n);It=!0;try{return e(t)}finally{if(It=!1,(null!==Rt||null!==Lt)&&(Bc(),Rt&&(t=Rt,e=Lt,Lt=Rt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Le]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Ut=!1;if(Mt)try{var Ft={};Object.defineProperty(Ft,"passive",{get:function(){Ut=!0}}),window.addEventListener("test",Ft,Ft),window.removeEventListener("test",Ft,Ft)}catch(Lh){Ut=!1}var Bt=null,Ht=null,qt=null;function Wt(){if(qt)return qt;var e,t,n=Ht,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return qt=a.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function $t(){return!0}function Kt(){return!1}function Gt(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?$t:Kt,this.isPropagationStopped=Kt,this}return h(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=$t)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=$t)},persist:function(){},isPersistent:$t}),t}var Jt,Qt,Yt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Gt(Xt),en=h({},Xt,{view:0,detail:0}),tn=Gt(en),nn=h({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yt&&(Yt&&"mousemove"===e.type?(Jt=e.screenX-Yt.screenX,Qt=e.screenY-Yt.screenY):Qt=Jt=0,Yt=e),Jt)},movementY:function(e){return"movementY"in e?e.movementY:Qt}}),rn=Gt(nn),an=Gt(h({},nn,{dataTransfer:0})),on=Gt(h({},en,{relatedTarget:0})),sn=Gt(h({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Gt(h({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Gt(h({},Xt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=hn[e])&&!!t[e]}function pn(){return fn}var mn=Gt(h({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Gt(h({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vn=Gt(h({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=Gt(h({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Gt(h({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Gt(h({},Xt,{newState:0,oldState:0})),kn=[9,13,27,32],xn=Mt&&"CompositionEvent"in window,Sn=null;Mt&&"documentMode"in document&&(Sn=document.documentMode);var _n=Mt&&"TextEvent"in window&&!Sn,jn=Mt&&(!xn||Sn&&8<Sn&&11>=Sn),En=String.fromCharCode(32),Nn=!1;function Cn(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Pn=!1;var On={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!On[e.type]:"textarea"===t}function Ln(e,t,n,r){Rt?Lt?Lt.push(r):Lt=[r]:Rt=r,0<(t=Wu(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,In=null;function zn(e){zu(e,0)}function Dn(e){if(ht(qe(e)))return e}function Mn(e,t){if("change"===e)return t}var Un=!1;if(Mt){var Fn;if(Mt){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Fn=Bn}else Fn=!1;Un=Fn&&(!document.documentMode||9<document.documentMode)}function qn(){An&&(An.detachEvent("onpropertychange",Wn),In=An=null)}function Wn(e){if("value"===e.propertyName&&Dn(In)){var t=[];Ln(t,In,e,Ot(e)),zt(zn,t)}}function Vn(e,t,n){"focusin"===e?(qn(),In=n,(An=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&qn()}function $n(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(In)}function Kn(e,t){if("click"===e)return Dn(t)}function Gn(e,t){if("input"===e||"change"===e)return Dn(t)}var Jn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Qn(e,t){if(Jn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Q.call(t,a)||!Jn(e[a],t[a]))return!1}return!0}function Yn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Yn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Yn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=ft((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=ft((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,or=null,ir=!1;function sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==ft(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},or&&Qn(or,r)||(or=r,0<(r=Wu(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},ur={},dr={};function hr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var fr=hr("animationend"),pr=hr("animationiteration"),mr=hr("animationstart"),gr=hr("transitionrun"),vr=hr("transitionstart"),yr=hr("transitioncancel"),br=hr("transitionend"),wr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xr(e,t){wr.set(e,t),Ge(t,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function _r(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var jr=[],Er=0,Nr=0;function Cr(){for(var e=Er,t=Nr=Er=0;t<e;){var n=jr[t];jr[t++]=null;var r=jr[t];jr[t++]=null;var a=jr[t];jr[t++]=null;var o=jr[t];if(jr[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==o&&Rr(n,a,o)}}function Tr(e,t,n,r){jr[Er++]=e,jr[Er++]=t,jr[Er++]=n,jr[Er++]=r,Nr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return Tr(e,t,n,r),Lr(e)}function Or(e,t){return Tr(e,null,null,t),Lr(e)}function Rr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,o=e.return;null!==o;)o.childLanes|=n,null!==(r=o.alternate)&&(r.childLanes|=n),22===o.tag&&(null===(e=o.stateNode)||1&e._visibility||(a=!0)),e=o,o=o.return;return 3===e.tag?(o=e.stateNode,a&&null!==t&&(a=31-fe(n),null===(r=(e=o.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),o):null}function Lr(e){if(50<Rc)throw Rc=0,Lc=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={};function Ir(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zr(e,t,n,r){return new Ir(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=zr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ur(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Fr(e,t,n,r,a,o){var s=0;if(r=e,"function"===typeof e)Dr(e)&&(s=1);else if("string"===typeof e)s=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case N:return(e=zr(31,n,t,a)).elementType=N,e.lanes=o,e;case g:return Br(n.children,a,o,t);case v:s=8,a|=24;break;case y:return(e=zr(12,n,t,2|a)).elementType=y,e.lanes=o,e;case S:return(e=zr(13,n,t,a)).elementType=S,e.lanes=o,e;case _:return(e=zr(19,n,t,a)).elementType=_,e.lanes=o,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case k:s=10;break e;case w:s=9;break e;case x:s=11;break e;case j:s=14;break e;case E:s=16,r=null;break e}s=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=zr(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Br(e,t,n,r){return(e=zr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=zr(6,e,null,t)).lanes=n,e}function qr(e,t,n){return(t=zr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],Vr=0,$r=null,Kr=0,Gr=[],Jr=0,Qr=null,Yr=1,Xr="";function Zr(e,t){Wr[Vr++]=Kr,Wr[Vr++]=$r,$r=e,Kr=t}function ea(e,t,n){Gr[Jr++]=Yr,Gr[Jr++]=Xr,Gr[Jr++]=Qr,Qr=e;var r=Yr;e=Xr;var a=32-fe(r)-1;r&=~(1<<a),n+=1;var o=32-fe(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Yr=1<<32-fe(t)+a|n<<a|r,Xr=o+e}else Yr=1<<o|n<<a|r,Xr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===$r;)$r=Wr[--Vr],Wr[Vr]=null,Kr=Wr[--Vr],Wr[Vr]=null;for(;e===Qr;)Qr=Gr[--Jr],Gr[Jr]=null,Xr=Gr[--Jr],Gr[Jr]=null,Yr=Gr[--Jr],Gr[Jr]=null}var ra=null,aa=null,oa=!1,ia=null,sa=!1,la=Error(i(519));function ca(e){throw ma(_r(Error(i(418,"")),e)),la}function ua(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Re]=e,t[Le]=r,n){case"dialog":Du("cancel",t),Du("close",t);break;case"iframe":case"object":case"embed":Du("load",t);break;case"video":case"audio":for(n=0;n<Au.length;n++)Du(Au[n],t);break;case"source":Du("error",t);break;case"img":case"image":case"link":Du("error",t),Du("load",t);break;case"details":Du("toggle",t);break;case"input":Du("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Du("invalid",t);break;case"textarea":Du("invalid",t),kt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Qu(t.textContent,n)?(null!=r.popover&&(Du("beforetoggle",t),Du("toggle",t)),null!=r.onScroll&&Du("scroll",t),null!=r.onScrollEnd&&Du("scrollend",t),null!=r.onClick&&(t.onclick=Yu),t=!0):t=!1,t||ca(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(sa=!1);case 27:case 3:return void(sa=!0);default:ra=ra.return}}function ha(e){if(e!==ra)return!1;if(!oa)return da(e),oa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&aa&&ca(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=vd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,fd(e.type)?(e=yd,yd=null,aa=e):aa=n):aa=ra?vd(e.stateNode.nextSibling):null;return!0}function fa(){aa=ra=null,oa=!1}function pa(){var e=ia;return null!==e&&(null===bc?bc=e:bc.push.apply(bc,e),ia=null),e}function ma(e){null===ia?ia=[e]:ia.push(e)}var ga=U(null),va=null,ya=null;function ba(e,t,n){B(ga,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ga.current,F(ga)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var s=a.child;o=o.firstContext;e:for(;null!==o;){var l=o;o=a;for(var c=0;c<t.length;c++)if(l.context===t[c]){o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),ka(o.return,n,e),r||(s=null);break e}o=l.next}}else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),ka(s,n,e),s=null}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===e){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}}function Sa(e,t,n,r){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(0!==(524288&a.flags))o=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var s=a.alternate;if(null===s)throw Error(i(387));if(null!==(s=s.memoizedProps)){var l=a.type;Jn(a.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(a===V.current){if(null===(s=a.alternate))throw Error(i(387));s.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Gd):e=[Gd])}a=a.return}null!==e&&xa(t,e,n,r),t.flags|=262144}function _a(e){for(e=e.firstContext;null!==e;){if(!Jn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ja(e){va=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ea(e){return Ca(va,e)}function Na(e,t){return null===va&&ja(e),Ca(e,t)}function Ca(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(i(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var Ta="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Pa=r.unstable_scheduleCallback,Oa=r.unstable_NormalPriority,Ra={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function La(){return{controller:new Ta,data:new Map,refCount:0}}function Aa(e){e.refCount--,0===e.refCount&&Pa(Oa,(function(){e.controller.abort()}))}var Ia=null,za=0,Da=0,Ma=null;function Ua(){if(0===--za&&null!==Ia){null!==Ma&&(Ma.status="fulfilled");var e=Ia;Ia=null,Da=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Fa=A.S;A.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Ia){var n=Ia=[];za=0,Da=Tu(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}za++,t.then(Ua,Ua)}(0,t),null!==Fa&&Fa(e,t)};var Ba=U(null);function Ha(){var e=Ba.current;return null!==e?e:rc.pooledCache}function qa(e,t){B(Ba,null===t?Ba.current:t.pool)}function Wa(){var e=Ha();return null===e?null:{parent:Ra._currentValue,pool:e}}var Va=Error(i(460)),$a=Error(i(474)),Ka=Error(i(542)),Ga={then:function(){}};function Ja(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Qa(){}function Ya(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Qa,Qa),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e;default:if("string"===typeof t.status)t.then(Qa,Qa);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e}throw Xa=t,Va}}var Xa=null;function Za(){if(null===Xa)throw Error(i(459));var e=Xa;return Xa=null,e}function eo(e){if(e===Va||e===Ka)throw Error(i(483))}var to=!1;function no(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ao(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nc)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Lr(e),Rr(e,null,n),t}return Tr(e,r,t,n),Lr(e)}function io(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ne(e,n)}}function so(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var lo=!1;function co(){if(lo){if(null!==Ma)throw Ma}}function uo(e,t,n,r){lo=!1;var a=e.updateQueue;to=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,c=l.next;l.next=null,null===i?o=c:i.next=c,i=l;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,u=c=l=null,s=o;;){var f=-536870913&s.lane,p=f!==s.lane;if(p?(oc&f)===f:(r&f)===f){0!==f&&f===Da&&(lo=!0),null!==u&&(u=u.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var m=e,g=s;f=t;var v=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(v,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=g.payload)?m.call(v,d,f):m)||void 0===f)break e;d=h({},d,f);break e;case 2:to=!0}}null!==(f=s.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=a.callbacks)?a.callbacks=[f]:p.push(f))}else p={lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=p,l=d):u=u.next=p,i|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(p=s).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}null===u&&(l=d),a.baseState=l,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===o&&(a.shared.lanes=0),fc|=i,e.lanes=i,e.memoizedState=d}}function ho(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function fo(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)ho(n[e],t)}var po=U(null),mo=U(0);function go(e,t){B(mo,e=dc),B(po,t),dc=e|t.baseLanes}function vo(){B(mo,dc),B(po,po.current)}function yo(){dc=mo.current,F(po),F(mo)}var bo=0,wo=null,ko=null,xo=null,So=!1,_o=!1,jo=!1,Eo=0,No=0,Co=null,To=0;function Po(){throw Error(i(321))}function Oo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Jn(e[n],t[n]))return!1;return!0}function Ro(e,t,n,r,a,o){return bo=o,wo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?$i:Ki,jo=!1,o=n(r,a),jo=!1,_o&&(o=Ao(t,n,r,a)),Lo(e),o}function Lo(e){A.H=Vi;var t=null!==ko&&null!==ko.next;if(bo=0,xo=ko=wo=null,So=!1,No=0,Co=null,t)throw Error(i(300));null===e||Ns||null!==(e=e.dependencies)&&_a(e)&&(Ns=!0)}function Ao(e,t,n,r){wo=e;var a=0;do{if(_o&&(Co=null),No=0,_o=!1,25<=a)throw Error(i(301));if(a+=1,xo=ko=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}A.H=Gi,o=t(n,r)}while(_o);return o}function Io(){var e=A.H,t=e.useState()[0];return t="function"===typeof t.then?Bo(t):t,e=e.useState()[0],(null!==ko?ko.memoizedState:null)!==e&&(wo.flags|=1024),t}function zo(){var e=0!==Eo;return Eo=0,e}function Do(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Mo(e){if(So){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}So=!1}bo=0,xo=ko=wo=null,_o=!1,No=Eo=0,Co=null}function Uo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===xo?wo.memoizedState=xo=e:xo=xo.next=e,xo}function Fo(){if(null===ko){var e=wo.alternate;e=null!==e?e.memoizedState:null}else e=ko.next;var t=null===xo?wo.memoizedState:xo.next;if(null!==t)xo=t,ko=e;else{if(null===e){if(null===wo.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(ko=e).memoizedState,baseState:ko.baseState,baseQueue:ko.baseQueue,queue:ko.queue,next:null},null===xo?wo.memoizedState=xo=e:xo=xo.next=e}return xo}function Bo(e){var t=No;return No+=1,null===Co&&(Co=[]),e=Ya(Co,e,t),t=wo,null===(null===xo?t.memoizedState:xo.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?$i:Ki),e}function Ho(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Bo(e);if(e.$$typeof===k)return Ea(e)}throw Error(i(438,String(e)))}function qo(e){var t=null,n=wo.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wo.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=C;return t.index++,n}function Wo(e,t){return"function"===typeof t?t(e):t}function Vo(e){return $o(Fo(),ko,e)}function $o(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var l=s=null,c=null,u=t=a.next,d=!1;do{var h=-536870913&u.lane;if(h!==u.lane?(oc&h)===h:(bo&h)===h){var f=u.revertLane;if(0===f)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),h===Da&&(d=!0);else{if((bo&f)===f){u=u.next,f===Da&&(d=!0);continue}h={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=h,s=o):c=c.next=h,wo.lanes|=f,fc|=f}h=u.action,jo&&n(o,h),o=u.hasEagerState?u.eagerState:n(o,h)}else f={lane:h,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=f,s=o):c=c.next=f,wo.lanes|=h,fc|=h;u=u.next}while(null!==u&&u!==t);if(null===c?s=o:c.next=l,!Jn(o,e.memoizedState)&&(Ns=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=o,e.baseState=s,e.baseQueue=c,r.lastRenderedState=o}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ko(e){var t=Fo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);Jn(o,t.memoizedState)||(Ns=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Go(e,t,n){var r=wo,a=Fo(),o=oa;if(o){if(void 0===n)throw Error(i(407));n=n()}else n=t();var s=!Jn((ko||a).memoizedState,n);if(s&&(a.memoizedState=n,Ns=!0),a=a.queue,vi(2048,8,Yo.bind(null,r,a,e),[e]),a.getSnapshot!==t||s||null!==xo&&1&xo.memoizedState.tag){if(r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Qo.bind(null,r,a,n,t),null),null===rc)throw Error(i(349));o||0!==(124&bo)||Jo(r,t,n)}return n}function Jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wo.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Qo(e,t,n,r){t.value=n,t.getSnapshot=r,Xo(t)&&Zo(e)}function Yo(e,t,n){return n((function(){Xo(t)&&Zo(e)}))}function Xo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Jn(e,n)}catch(r){return!0}}function Zo(e){var t=Or(e,2);null!==t&&zc(t,e,2)}function ei(e){var t=Uo();if("function"===typeof e){var n=e;if(e=n(),jo){he(!0);try{n()}finally{he(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,$o(e,ko,"function"===typeof r?r:Wo)}function ni(e,t,n,r,a){if(Hi(e))throw Error(i(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==A.T?n(!0):o.isTransition=!1,r(o),null===(n=t.pending)?(o.next=t.pending=o,ri(t,o)):(o.next=n.next,t.pending=n.next=o)}}function ri(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var o=A.T,i={};A.T=i;try{var s=n(a,r),l=A.S;null!==l&&l(i,s),ai(e,t,s)}catch(c){ii(e,t,c)}finally{A.T=o}}else try{ai(e,t,o=n(a,r))}catch(u){ii(e,t,u)}}function ai(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then((function(n){oi(e,t,n)}),(function(n){return ii(e,t,n)})):oi(e,t,n)}function oi(e,t,n){t.status="fulfilled",t.value=n,si(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,si(t),t=t.next}while(t!==r)}e.action=null}function si(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function li(e,t){return t}function ci(e,t){if(oa){var n=rc.formState;if(null!==n){e:{var r=wo;if(oa){if(aa){t:{for(var a=aa,o=sa;8!==a.nodeType;){if(!o){a=null;break t}if(null===(a=vd(a.nextSibling))){a=null;break t}}a="F!"===(o=a.data)||"F"===o?a:null}if(a){aa=vd(a.nextSibling),r="F!"===a.data;break e}}ca(r)}r=!1}r&&(t=n[0])}}return(n=Uo()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:t},n.queue=r,n=Ui.bind(null,wo,r),r.dispatch=n,r=ei(!1),o=Bi.bind(null,wo,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Uo()).queue=a,n=ni.bind(null,wo,a,o,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ui(e){return di(Fo(),ko,e)}function di(e,t,n){if(t=$o(e,t,li)[0],e=Vo(Wo)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Bo(t)}catch(i){if(i===Va)throw Ka;throw i}else r=t;var a=(t=Fo()).queue,o=a.dispatch;return n!==t.memoizedState&&(wo.flags|=2048,pi(9,{destroy:void 0,resource:void 0},hi.bind(null,a,n),null)),[r,o,e]}function hi(e,t){e.action=t}function fi(e){var t=Fo(),n=ko;if(null!==n)return di(t,n,e);Fo(),t=t.memoizedState;var r=(n=Fo()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function pi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wo.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mi(){return Fo().memoizedState}function gi(e,t,n,r){var a=Uo();r=void 0===r?null:r,wo.flags|=e,a.memoizedState=pi(1|t,{destroy:void 0,resource:void 0},n,r)}function vi(e,t,n,r){var a=Fo();r=void 0===r?null:r;var o=a.memoizedState.inst;null!==ko&&null!==r&&Oo(r,ko.memoizedState.deps)?a.memoizedState=pi(t,o,n,r):(wo.flags|=e,a.memoizedState=pi(1|t,o,n,r))}function yi(e,t){gi(8390656,8,e,t)}function bi(e,t){vi(2048,8,e,t)}function wi(e,t){return vi(4,2,e,t)}function ki(e,t){return vi(4,4,e,t)}function xi(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function Si(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,vi(4,4,xi.bind(null,t,e),n)}function _i(){}function ji(e,t){var n=Fo();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Oo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ei(e,t){var n=Fo();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Oo(t,r[1]))return r[0];if(r=e(),jo){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[r,t],r}function Ni(e,t,n){return void 0===n||0!==(1073741824&bo)?e.memoizedState=t:(e.memoizedState=n,e=Ic(),wo.lanes|=e,fc|=e,n)}function Ci(e,t,n,r){return Jn(n,t)?n:null!==po.current?(e=Ni(e,n,r),Jn(e,t)||(Ns=!0),e):0===(42&bo)?(Ns=!0,e.memoizedState=n):(e=Ic(),wo.lanes|=e,fc|=e,t)}function Ti(e,t,n,r,a){var o=I.p;I.p=0!==o&&8>o?o:8;var i=A.T,s={};A.T=s,Bi(e,!1,t,n);try{var l=a(),c=A.S;if(null!==c&&c(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Fi(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then((function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)}),(function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)})),r}(l,r),Ac());else Fi(e,t,r,Ac())}catch(u){Fi(e,t,{then:function(){},status:"rejected",reason:u},Ac())}finally{I.p=o,A.T=i}}function Pi(){}function Oi(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=Ri(e).queue;Ti(e,a,t,z,null===n?Pi:function(){return Li(e),n(r)})}function Ri(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:z,baseState:z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:z},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Li(e){Fi(e,Ri(e).next.queue,{},Ac())}function Ai(){return Ea(Gd)}function Ii(){return Fo().memoizedState}function zi(){return Fo().memoizedState}function Di(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Ac(),r=oo(t,e=ao(n),n);return null!==r&&(zc(r,t,n),io(r,t,n)),t={cache:La()},void(e.payload=t)}t=t.return}}function Mi(e,t,n){var r=Ac();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e)?qi(t,n):null!==(n=Pr(e,t,n,r))&&(zc(n,e,r),Wi(n,t,r))}function Ui(e,t,n){Fi(e,t,n,Ac())}function Fi(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))qi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,Jn(s,i))return Tr(e,t,a,0),null===rc&&Cr(),!1}catch(l){}if(null!==(n=Pr(e,t,a,r)))return zc(n,e,r),Wi(n,t,r),!0}return!1}function Bi(e,t,n,r){if(r={lane:2,revertLane:Tu(),action:r,hasEagerState:!1,eagerState:null,next:null},Hi(e)){if(t)throw Error(i(479))}else null!==(t=Pr(e,n,r,2))&&zc(t,e,2)}function Hi(e){var t=e.alternate;return e===wo||null!==t&&t===wo}function qi(e,t){_o=So=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ne(e,n)}}var Vi={readContext:Ea,use:Ho,useCallback:Po,useContext:Po,useEffect:Po,useImperativeHandle:Po,useLayoutEffect:Po,useInsertionEffect:Po,useMemo:Po,useReducer:Po,useRef:Po,useState:Po,useDebugValue:Po,useDeferredValue:Po,useTransition:Po,useSyncExternalStore:Po,useId:Po,useHostTransitionStatus:Po,useFormState:Po,useActionState:Po,useOptimistic:Po,useMemoCache:Po,useCacheRefresh:Po},$i={readContext:Ea,use:Ho,useCallback:function(e,t){return Uo().memoizedState=[e,void 0===t?null:t],e},useContext:Ea,useEffect:yi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4194308,4,xi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return gi(4194308,4,e,t)},useInsertionEffect:function(e,t){gi(4,2,e,t)},useMemo:function(e,t){var n=Uo();t=void 0===t?null:t;var r=e();if(jo){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Uo();if(void 0!==n){var a=n(t);if(jo){he(!0);try{n(t)}finally{he(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mi.bind(null,wo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Uo().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Ui.bind(null,wo,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:_i,useDeferredValue:function(e,t){return Ni(Uo(),e,t)},useTransition:function(){var e=ei(!1);return e=Ti.bind(null,wo,e.queue,!0,!1),Uo().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wo,a=Uo();if(oa){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===rc)throw Error(i(349));0!==(124&oc)||Jo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,yi(Yo.bind(null,r,o,e),[e]),r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Qo.bind(null,r,o,n,t),null),n},useId:function(){var e=Uo(),t=rc.identifierPrefix;if(oa){var n=Xr;t="\xab"+t+"R"+(n=(Yr&~(1<<32-fe(Yr)-1)).toString(32)+n),0<(n=Eo++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=To++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Ai,useFormState:ci,useActionState:ci,useOptimistic:function(e){var t=Uo();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bi.bind(null,wo,!0,n),n.dispatch=t,[e,t]},useMemoCache:qo,useCacheRefresh:function(){return Uo().memoizedState=Di.bind(null,wo)}},Ki={readContext:Ea,use:Ho,useCallback:ji,useContext:Ea,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ei,useReducer:Vo,useRef:mi,useState:function(){return Vo(Wo)},useDebugValue:_i,useDeferredValue:function(e,t){return Ci(Fo(),ko.memoizedState,e,t)},useTransition:function(){var e=Vo(Wo)[0],t=Fo().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Go,useId:Ii,useHostTransitionStatus:Ai,useFormState:ui,useActionState:ui,useOptimistic:function(e,t){return ti(Fo(),0,e,t)},useMemoCache:qo,useCacheRefresh:zi},Gi={readContext:Ea,use:Ho,useCallback:ji,useContext:Ea,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ei,useReducer:Ko,useRef:mi,useState:function(){return Ko(Wo)},useDebugValue:_i,useDeferredValue:function(e,t){var n=Fo();return null===ko?Ni(n,e,t):Ci(n,ko.memoizedState,e,t)},useTransition:function(){var e=Ko(Wo)[0],t=Fo().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Go,useId:Ii,useHostTransitionStatus:Ai,useFormState:fi,useActionState:fi,useOptimistic:function(e,t){var n=Fo();return null!==ko?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:qo,useCacheRefresh:zi},Ji=null,Qi=0;function Yi(e){var t=Qi;return Qi+=1,null===Ji&&(Ji=[]),Ya(Ji,e,t)}function Xi(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===f)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ts(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var o=n.type;return o===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===E&&es(o)===t.type)?(Xi(t=a(t,n.props),n),t.return=e,t):(Xi(t=Fr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Br(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function h(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Xi(n=Fr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=qr(t,e.mode,n)).return=e,t;case E:return h(e,t=(0,t._init)(t._payload),n)}if(L(t)||P(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return h(e,Yi(t),n);if(t.$$typeof===k)return h(e,Na(e,t),n);Zi(e,t)}return null}function f(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?c(e,t,n,r):null;case m:return n.key===a?u(e,t,n,r):null;case E:return f(e,t,n=(a=n._init)(n._payload),r)}if(L(n)||P(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return f(e,t,Yi(n),r);if(n.$$typeof===k)return f(e,t,Na(e,n),r);Zi(e,n)}return null}function v(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case p:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case m:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case E:return v(e,t,n,r=(0,r._init)(r._payload),a)}if(L(r)||P(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return v(e,t,n,Yi(r),a);if(r.$$typeof===k)return v(e,t,n,Na(t,r),a);Zi(t,r)}return null}function y(l,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case p:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===g){if(7===c.tag){n(l,c.sibling),(d=a(c,u.props.children)).return=l,l=d;break e}}else if(c.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===E&&es(b)===c.type){n(l,c.sibling),Xi(d=a(c,u.props),u),d.return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}u.type===g?((d=Br(u.props.children,l.mode,d,u.key)).return=l,l=d):(Xi(d=Fr(u.type,u.key,u.props,null,l.mode,d),u),d.return=l,l=d)}return s(l);case m:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(l,c.sibling),(d=a(c,u.children||[])).return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}(d=qr(u,l.mode,d)).return=l,l=d}return s(l);case E:return y(l,c,u=(b=u._init)(u._payload),d)}if(L(u))return function(a,i,s,l){for(var c=null,u=null,d=i,p=i=0,m=null;null!==d&&p<s.length;p++){d.index>p?(m=d,d=null):m=d.sibling;var g=f(a,d,s[p],l);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(a,d),i=o(g,i,p),null===u?c=g:u.sibling=g,u=g,d=m}if(p===s.length)return n(a,d),oa&&Zr(a,p),c;if(null===d){for(;p<s.length;p++)null!==(d=h(a,s[p],l))&&(i=o(d,i,p),null===u?c=d:u.sibling=d,u=d);return oa&&Zr(a,p),c}for(d=r(d);p<s.length;p++)null!==(m=v(d,a,p,s[p],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?p:m.key),i=o(m,i,p),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach((function(e){return t(a,e)})),oa&&Zr(a,p),c}(l,c,u,d);if(P(u)){if("function"!==typeof(b=P(u)))throw Error(i(150));return function(a,s,l,c){if(null==l)throw Error(i(151));for(var u=null,d=null,p=s,m=s=0,g=null,y=l.next();null!==p&&!y.done;m++,y=l.next()){p.index>m?(g=p,p=null):g=p.sibling;var b=f(a,p,y.value,c);if(null===b){null===p&&(p=g);break}e&&p&&null===b.alternate&&t(a,p),s=o(b,s,m),null===d?u=b:d.sibling=b,d=b,p=g}if(y.done)return n(a,p),oa&&Zr(a,m),u;if(null===p){for(;!y.done;m++,y=l.next())null!==(y=h(a,y.value,c))&&(s=o(y,s,m),null===d?u=y:d.sibling=y,d=y);return oa&&Zr(a,m),u}for(p=r(p);!y.done;m++,y=l.next())null!==(y=v(p,a,m,y.value,c))&&(e&&null!==y.alternate&&p.delete(null===y.key?m:y.key),s=o(y,s,m),null===d?u=y:d.sibling=y,d=y);return e&&p.forEach((function(e){return t(a,e)})),oa&&Zr(a,m),u}(l,c,u=b.call(u),d)}if("function"===typeof u.then)return y(l,c,Yi(u),d);if(u.$$typeof===k)return y(l,c,Na(l,u),d);Zi(l,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(l,c.sibling),(d=a(c,u)).return=l,l=d):(n(l,c),(d=Hr(u,l.mode,d)).return=l,l=d),s(l)):n(l,c)}return function(e,t,n,r){try{Qi=0;var a=y(e,t,n,r);return Ji=null,a}catch(i){if(i===Va||i===Ka)throw i;var o=zr(29,i,null,e.mode);return o.lanes=r,o.return=e,o}}}var ns=ts(!0),rs=ts(!1),as=U(null),os=null;function is(e){var t=e.alternate;B(us,1&us.current),B(as,e),null===os&&(null===t||null!==po.current||null!==t.memoizedState)&&(os=e)}function ss(e){if(22===e.tag){if(B(us,us.current),B(as,e),null===os){var t=e.alternate;null!==t&&null!==t.memoizedState&&(os=e)}}else ls()}function ls(){B(us,us.current),B(as,as.current)}function cs(e){F(as),os===e&&(os=null),F(us)}var us=U(0);function ds(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:h({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var fs={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ac(),a=ao(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(zc(t,e,r),io(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ac(),a=ao(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(zc(t,e,r),io(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ac(),r=ao(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=oo(e,r,n))&&(zc(t,e,n),io(t,e,n))}};function ps(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!Qn(n,r)||!Qn(a,o))}function ms(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&fs.enqueueReplaceState(t,t.state,null)}function gs(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=h({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var vs="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function ys(e){vs(e)}function bs(e){console.error(e)}function ws(e){vs(e)}function ks(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function xs(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Ss(e,t,n){return(n=ao(n)).tag=3,n.payload={element:null},n.callback=function(){ks(e,t)},n}function _s(e){return(e=ao(e)).tag=3,e}function js(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var o=r.value;e.payload=function(){return a(o)},e.callback=function(){xs(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){xs(t,n,r),"function"!==typeof a&&(null===_c?_c=new Set([this]):_c.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Es=Error(i(461)),Ns=!1;function Cs(e,t,n,r){t.child=null===e?rs(t,null,n,r):ns(t,e.child,n,r)}function Ts(e,t,n,r,a){n=n.render;var o=t.ref;if("ref"in r){var i={};for(var s in r)"ref"!==s&&(i[s]=r[s])}else i=r;return ja(t),r=Ro(e,t,n,i,o,a),s=zo(),null===e||Ns?(oa&&s&&ta(t),t.flags|=1,Cs(e,t,r,a),t.child):(Do(e,t,a),Js(e,t,a))}function Ps(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Dr(o)||void 0!==o.defaultProps||null!==n.compare?((e=Fr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Os(e,t,o,r,a))}if(o=e.child,!Qs(e,a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:Qn)(i,r)&&e.ref===t.ref)return Js(e,t,a)}return t.flags|=1,(e=Mr(o,r)).ref=t.ref,e.return=t,t.child=e}function Os(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(Qn(o,r)&&e.ref===t.ref){if(Ns=!1,t.pendingProps=r=o,!Qs(e,a))return t.lanes=e.lanes,Js(e,t,a);0!==(131072&e.flags)&&(Ns=!0)}}return Is(e,t,n,r,a)}function Rs(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==o?o.baseLanes|n:n,null!==e){for(a=t.child=e.child,o=0;null!==a;)o=o|a.lanes|a.childLanes,a=a.sibling;t.childLanes=o&~r}else t.childLanes=0,t.child=null;return Ls(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ls(e,t,null!==o?o.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qa(0,null!==o?o.cachePool:null),null!==o?go(t,o):vo(),ss(t)}else null!==o?(qa(0,o.cachePool),go(t,o),ls(),t.memoizedState=null):(null!==e&&qa(0,null),vo(),ls());return Cs(e,t,a,n),t.child}function Ls(e,t,n,r){var a=Ha();return a=null===a?null:{parent:Ra._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&qa(0,null),vo(),ss(t),null!==e&&Sa(e,t,r,!0),null}function As(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Is(e,t,n,r,a){return ja(t),n=Ro(e,t,n,r,void 0,a),r=zo(),null===e||Ns?(oa&&r&&ta(t),t.flags|=1,Cs(e,t,n,a),t.child):(Do(e,t,a),Js(e,t,a))}function zs(e,t,n,r,a,o){return ja(t),t.updateQueue=null,n=Ao(t,r,n,a),Lo(e),r=zo(),null===e||Ns?(oa&&r&&ta(t),t.flags|=1,Cs(e,t,n,o),t.child):(Do(e,t,o),Js(e,t,o))}function Ds(e,t,n,r,a){if(ja(t),null===t.stateNode){var o=Ar,i=n.contextType;"object"===typeof i&&null!==i&&(o=Ea(i)),o=new n(r,o),t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,o.updater=fs,t.stateNode=o,o._reactInternals=t,(o=t.stateNode).props=r,o.state=t.memoizedState,o.refs={},no(t),i=n.contextType,o.context="object"===typeof i&&null!==i?Ea(i):Ar,o.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(hs(t,n,i,r),o.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(i=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),i!==o.state&&fs.enqueueReplaceState(o,o.state,null),uo(t,r,o,a),co(),o.state=t.memoizedState),"function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){o=t.stateNode;var s=t.memoizedProps,l=gs(n,s);o.props=l;var c=o.context,u=n.contextType;i=Ar,"object"===typeof u&&null!==u&&(i=Ea(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof o.getSnapshotBeforeUpdate,s=t.pendingProps!==s,u||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s||c!==i)&&ms(t,o,r,i),to=!1;var h=t.memoizedState;o.state=h,uo(t,r,o,a),co(),c=t.memoizedState,s||h!==c||to?("function"===typeof d&&(hs(t,n,d,r),c=t.memoizedState),(l=to||ps(t,n,l,r,h,c,i))?(u||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=i,r=l):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ro(e,t),u=gs(n,i=t.memoizedProps),o.props=u,d=t.pendingProps,h=o.context,c=n.contextType,l=Ar,"object"===typeof c&&null!==c&&(l=Ea(c)),(c="function"===typeof(s=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||h!==l)&&ms(t,o,r,l),to=!1,h=t.memoizedState,o.state=h,uo(t,r,o,a),co();var f=t.memoizedState;i!==d||h!==f||to||null!==e&&null!==e.dependencies&&_a(e.dependencies)?("function"===typeof s&&(hs(t,n,s,r),f=t.memoizedState),(u=to||ps(t,n,u,r,h,f,l)||null!==e&&null!==e.dependencies&&_a(e.dependencies))?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,f,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,f,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=f),o.props=r,o.state=f,o.context=l,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return o=r,As(e,t),r=0!==(128&t.flags),o||r?(o=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:o.render(),t.flags|=1,null!==e&&r?(t.child=ns(t,e.child,null,a),t.child=ns(t,null,n,a)):Cs(e,t,n,a),t.memoizedState=o.state,e=t.child):e=Js(e,t,a),e}function Ms(e,t,n,r){return fa(),t.flags|=256,Cs(e,t,n,r),t.child}var Us={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Fs(e){return{baseLanes:e,cachePool:Wa()}}function Bs(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gc),e}function Hs(e,t,n){var r,a=t.pendingProps,o=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&us.current)),r&&(o=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(oa){if(o?is(t):ls(),oa){var l,c=aa;if(l=c){e:{for(l=c,c=sa;8!==l.nodeType;){if(!c){c=null;break e}if(null===(l=vd(l.nextSibling))){c=null;break e}}c=l}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Qr?{id:Yr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(l=zr(18,null,null,0)).stateNode=c,l.return=t,t.child=l,ra=t,aa=null,l=!0):l=!1}l||ca(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?t.lanes=32:t.lanes=536870912,null;cs(t)}return c=a.children,a=a.fallback,o?(ls(),c=Ws({mode:"hidden",children:c},o=t.mode),a=Br(a,o,n,null),c.return=t,a.return=t,c.sibling=a,t.child=c,(o=t.child).memoizedState=Fs(n),o.childLanes=Bs(e,r,n),t.memoizedState=Us,a):(is(t),qs(t,c))}if(null!==(l=e.memoizedState)&&null!==(c=l.dehydrated)){if(s)256&t.flags?(is(t),t.flags&=-257,t=Vs(e,t,n)):null!==t.memoizedState?(ls(),t.child=e.child,t.flags|=128,t=null):(ls(),o=a.fallback,c=t.mode,a=Ws({mode:"visible",children:a.children},c),(o=Br(o,c,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,ns(t,e.child,null,n),(a=t.child).memoizedState=Fs(n),a.childLanes=Bs(e,r,n),t.memoizedState=Us,t=o);else if(is(t),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(a=Error(i(419))).stack="",a.digest=r,ma({value:a,source:null,stack:null}),t=Vs(e,t,n)}else if(Ns||Sa(e,t,n,!1),r=0!==(n&e.childLanes),Ns||r){if(null!==(r=rc)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Ce(a))&(r.suspendedLanes|n))?0:a)&&a!==l.retryLane))throw l.retryLane=a,Or(e,a),zc(r,e,a),Es;"$?"===c.data||Kc(),t=Vs(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,aa=vd(c.nextSibling),ra=t,oa=!0,ia=null,sa=!1,null!==e&&(Gr[Jr++]=Yr,Gr[Jr++]=Xr,Gr[Jr++]=Qr,Yr=e.id,Xr=e.overflow,Qr=t),(t=qs(t,a.children)).flags|=4096);return t}return o?(ls(),o=a.fallback,c=t.mode,u=(l=e.child).sibling,(a=Mr(l,{mode:"hidden",children:a.children})).subtreeFlags=65011712&l.subtreeFlags,null!==u?o=Mr(u,o):(o=Br(o,c,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(c=e.child.memoizedState)?c=Fs(n):(null!==(l=c.cachePool)?(u=Ra._currentValue,l=l.parent!==u?{parent:u,pool:u}:l):l=Wa(),c={baseLanes:c.baseLanes|n,cachePool:l}),o.memoizedState=c,o.childLanes=Bs(e,r,n),t.memoizedState=Us,a):(is(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function qs(e,t){return(t=Ws({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Ws(e,t){return(e=zr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vs(e,t,n){return ns(t,e.child,null,n),(e=qs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function $s(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Ks(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Gs(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Cs(e,t,r.children,n),0!==(2&(r=us.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&$s(e,n,t);else if(19===e.tag)$s(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(us,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ds(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ks(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ds(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ks(t,!0,n,null,o);break;case"together":Ks(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Js(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),fc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Qs(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!_a(e))}function Ys(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ns=!0;else{if(!Qs(e,n)&&0===(128&t.flags))return Ns=!1,function(e,t,n){switch(t.tag){case 3:$(t,t.stateNode.containerInfo),ba(0,Ra,e.memoizedState.cache),fa();break;case 27:case 5:G(t);break;case 4:$(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(is(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Hs(e,t,n):(is(t),null!==(e=Js(e,t,n))?e.sibling:null);is(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Gs(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(us,us.current),r)break;return null;case 22:case 23:return t.lanes=0,Rs(e,t,n);case 24:ba(0,Ra,e.memoizedState.cache)}return Js(e,t,n)}(e,t,n);Ns=0!==(131072&e.flags)}else Ns=!1,oa&&0!==(1048576&t.flags)&&ea(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===x){t.tag=11,t=Ts(null,t,r,e,n);break e}if(a===j){t.tag=14,t=Ps(null,t,r,e,n);break e}}throw t=R(r)||r,Error(i(306,t,""))}Dr(r)?(e=gs(r,e),t.tag=1,t=Ds(null,t,r,e,n)):(t.tag=0,t=Is(null,t,r,e,n))}return t;case 0:return Is(e,t,t.type,t.pendingProps,n);case 1:return Ds(e,t,r=t.type,a=gs(r,t.pendingProps),n);case 3:e:{if($(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,ro(e,t),uo(t,r,null,n);var s=t.memoizedState;if(r=s.cache,ba(0,Ra,r),r!==o.cache&&xa(t,[Ra],n,!0),co(),r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ms(e,t,r,n);break e}if(r!==a){ma(a=_r(Error(i(424)),t)),t=Ms(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=vd(e.firstChild),ra=t,oa=!0,ia=null,sa=!0,n=rs(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(fa(),r===a){t=Js(e,t,n);break e}Cs(e,t,r,n)}t=t.child}return t;case 26:return As(e,t),null===e?(n=Cd(t.type,null,t.pendingProps,null))?t.memoizedState=n:oa||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[Re]=t,r[Le]=e,ed(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=Cd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return G(t),null===e&&oa&&(r=t.stateNode=wd(t.type,t.pendingProps,W.current),ra=t,sa=!0,a=aa,fd(t.type)?(yd=a,aa=vd(r.firstChild)):aa=a),Cs(e,t,t.pendingProps.children,n),As(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&oa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ue])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(o=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(o!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((o=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var o=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===o)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,sa))?(t.stateNode=r,ra=t,aa=vd(r.firstChild),sa=!1,a=!0):a=!1),a||ca(t)),G(t),a=t.type,o=t.pendingProps,s=null!==e?e.memoizedProps:null,r=o.children,id(a,o)?r=null:null!==s&&id(a,s)&&(t.flags|=32),null!==t.memoizedState&&(a=Ro(e,t,Io,null,null,n),Gd._currentValue=a),As(e,t),Cs(e,t,r,n),t.child;case 6:return null===e&&oa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(n,t.pendingProps,sa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ca(t)),null;case 13:return Hs(e,t,n);case 4:return $(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ns(t,null,r,n):Cs(e,t,r,n),t.child;case 11:return Ts(e,t,t.type,t.pendingProps,n);case 7:return Cs(e,t,t.pendingProps,n),t.child;case 8:case 12:return Cs(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Cs(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,ja(t),r=r(a=Ea(a)),t.flags|=1,Cs(e,t,r,n),t.child;case 14:return Ps(e,t,t.type,t.pendingProps,n);case 15:return Os(e,t,t.type,t.pendingProps,n);case 19:return Gs(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Ws(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Rs(e,t,n);case 24:return ja(t),r=Ea(Ra),null===e?(null===(a=Ha())&&(a=rc,o=La(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},no(t),ba(0,Ra,a)):(0!==(e.lanes&n)&&(ro(e,t),uo(t,null,null,n),co()),a=e.memoizedState,o=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Ra,r)):(r=o.cache,ba(0,Ra,r),r!==a.cache&&xa(t,[Ra],n,!0))),Cs(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Xs(e){e.flags|=4}function Zs(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=as.current)&&((4194048&oc)===oc?null!==os:(62914560&oc)!==oc&&0===(536870912&oc)||t!==os))throw Xa=Ga,$a;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,vc|=t)}function tl(e,t){if(!oa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(Ra),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(ha(t)?Xs(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pa())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Xs(t),null!==n?(nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Xs(t),nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Xs(t),nl(t),t.flags&=-16777217),null;case 27:J(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}e=H.current,ha(t)?ua(t):(e=wd(a,r,n),t.stateNode=e,Xs(t))}return nl(t),null;case 5:if(J(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}if(e=H.current,ha(t))ua(t);else{switch(a=rd(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Re]=t,e[Le]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xs(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=W.current,ha(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Qu(e.nodeValue,n)))||ca(t)}else(e=rd(e).createTextNode(r))[Re]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=ha(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[Re]=t}else fa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(cs(t),t):(cs(t),null)}if(cs(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return K(),null===e&&Fu(t.stateNode.containerInfo),nl(t),null;case 10:return wa(t.type),nl(t),null;case 19:if(F(us),null===(a=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(o=a.rendering))if(r)tl(a,!1);else{if(0!==hc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=ds(e))){for(t.flags|=128,tl(a,!1),e=o.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ur(n,e),n=n.sibling;return B(us,1&us.current|2),t.child}e=e.sibling}null!==a.tail&&te()>xc&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ds(o))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!oa)return nl(t),null}else 2*te()-a.renderingStartTime>xc&&536870912!==n&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=us.current,B(us,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return cs(t),yo(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&F(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(Ra),nl(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function al(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(Ra),K(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return J(t),null;case 13:if(cs(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));fa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return F(us),null;case 4:return K(),null;case 10:return wa(t.type),null;case 22:case 23:return cs(t),yo(),null!==e&&F(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(Ra),null;default:return null}}function ol(e,t){switch(na(t),t.tag){case 3:wa(Ra),K();break;case 26:case 27:case 5:J(t);break;case 4:K();break;case 13:cs(t);break;case 19:F(us);break;case 10:wa(t.type);break;case 22:case 23:cs(t),yo(),null!==e&&F(Ba);break;case 24:wa(Ra)}}function il(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var o=n.create,i=n.inst;r=o(),i.destroy=r}n=n.next}while(n!==a)}}catch(s){uu(t,t.return,s)}}function sl(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var o=a.next;r=o;do{if((r.tag&e)===e){var i=r.inst,s=i.destroy;if(void 0!==s){i.destroy=void 0,a=t;var l=n,c=s;try{c()}catch(u){uu(a,l,u)}}}r=r.next}while(r!==o)}}catch(u){uu(t,t.return,u)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fo(t,n)}catch(r){uu(e,e.return,r)}}}function cl(e,t,n){n.props=gs(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){uu(e,t,r)}}function ul(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){uu(e,t,a)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){uu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(o){uu(e,t,o)}else n.current=null}function hl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){uu(e,e.return,a)}}function fl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,s=null,l=null,c=null,u=null,d=null;for(p in n){var h=n[p];if(n.hasOwnProperty(p)&&null!=h)switch(p){case"checked":case"value":break;case"defaultValue":c=h;default:r.hasOwnProperty(p)||Xu(e,t,p,null,r,h)}}for(var f in r){var p=r[f];if(h=n[f],r.hasOwnProperty(f)&&(null!=p||null!=h))switch(f){case"type":o=p;break;case"name":a=p;break;case"checked":u=p;break;case"defaultChecked":d=p;break;case"value":s=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:p!==h&&Xu(e,t,f,p,r,h)}}return void gt(e,s,l,c,u,d,o,a);case"select":for(o in p=s=l=f=null,n)if(c=n[o],n.hasOwnProperty(o)&&null!=c)switch(o){case"value":break;case"multiple":p=c;default:r.hasOwnProperty(o)||Xu(e,t,o,null,r,c)}for(a in r)if(o=r[a],c=n[a],r.hasOwnProperty(a)&&(null!=o||null!=c))switch(a){case"value":f=o;break;case"defaultValue":l=o;break;case"multiple":s=o;default:o!==c&&Xu(e,t,a,o,r,c)}return t=l,n=s,r=p,void(null!=f?bt(e,!!n,f,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=f=null,n)if(a=n[l],n.hasOwnProperty(l)&&null!=a&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Xu(e,t,l,null,r,a)}for(s in r)if(a=r[s],o=n[s],r.hasOwnProperty(s)&&(null!=a||null!=o))switch(s){case"value":f=a;break;case"defaultValue":p=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==o&&Xu(e,t,s,a,r,o)}return void wt(e,f,p);case"option":for(var m in n)if(f=n[m],n.hasOwnProperty(m)&&null!=f&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Xu(e,t,m,null,r,f);for(c in r)if(f=r[c],p=n[c],r.hasOwnProperty(c)&&f!==p&&(null!=f||null!=p))if("selected"===c)e.selected=f&&"function"!==typeof f&&"symbol"!==typeof f;else Xu(e,t,c,f,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)f=n[g],n.hasOwnProperty(g)&&null!=f&&!r.hasOwnProperty(g)&&Xu(e,t,g,null,r,f);for(u in r)if(f=r[u],p=n[u],r.hasOwnProperty(u)&&f!==p&&(null!=f||null!=p))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(i(137,t));break;default:Xu(e,t,u,f,r,p)}return;default:if(Et(t)){for(var v in n)f=n[v],n.hasOwnProperty(v)&&void 0!==f&&!r.hasOwnProperty(v)&&Zu(e,t,v,void 0,r,f);for(d in r)f=r[d],p=n[d],!r.hasOwnProperty(d)||f===p||void 0===f&&void 0===p||Zu(e,t,d,f,r,p);return}}for(var y in n)f=n[y],n.hasOwnProperty(y)&&null!=f&&!r.hasOwnProperty(y)&&Xu(e,t,y,null,r,f);for(h in r)f=r[h],p=n[h],!r.hasOwnProperty(h)||f===p||null==f&&null==p||Xu(e,t,h,f,r,p)}(r,e.type,n,t),r[Le]=t}catch(a){uu(e,e.return,a)}}function pl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&fd(e.type)||4===e.tag}function ml(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||pl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&fd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Yu));else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gl(e,t,n),e=e.sibling;null!==e;)gl(e,t,n),e=e.sibling}function vl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(vl(e,t,n),e=e.sibling;null!==e;)vl(e,t,n),e=e.sibling}function yl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Re]=e,t[Le]=n}catch(o){uu(e,e.return,o)}}var bl=!1,wl=!1,kl=!1,xl="function"===typeof WeakSet?WeakSet:Set,Sl=null;function _l(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Dl(e,n),4&r&&il(5,n);break;case 1:if(Dl(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){uu(n,n.return,i)}else{var a=gs(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){uu(n,n.return,s)}}64&r&&ll(n),512&r&&ul(n,n.return);break;case 3:if(Dl(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fo(e,t)}catch(i){uu(n,n.return,i)}}break;case 27:null===t&&4&r&&yl(n);case 26:case 5:Dl(e,n),null===t&&4&r&&hl(n),512&r&&ul(n,n.return);break;case 12:Dl(e,n);break;case 13:Dl(e,n),4&r&&Pl(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,a=bl;var o=wl;bl=r,(wl=t)&&!o?Ul(e,n,0!==(8772&n.subtreeFlags)):Dl(e,n),bl=a,wl=o}break;case 30:break;default:Dl(e,n)}}function jl(e){var t=e.alternate;null!==t&&(e.alternate=null,jl(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Fe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var El=null,Nl=!1;function Cl(e,t,n){for(n=n.child;null!==n;)Tl(e,t,n),n=n.sibling}function Tl(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(o){}switch(n.tag){case 26:wl||dl(n,t),Cl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||dl(n,t);var r=El,a=Nl;fd(n.type)&&(El=n.stateNode,Nl=!1),Cl(e,t,n),kd(n.stateNode),El=r,Nl=a;break;case 5:wl||dl(n,t);case 6:if(r=El,a=Nl,El=null,Cl(e,t,n),Nl=a,null!==(El=r))if(Nl)try{(9===El.nodeType?El.body:"HTML"===El.nodeName?El.ownerDocument.body:El).removeChild(n.stateNode)}catch(i){uu(n,t,i)}else try{El.removeChild(n.stateNode)}catch(i){uu(n,t,i)}break;case 18:null!==El&&(Nl?(pd(9===(e=El).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Nh(e)):pd(El,n.stateNode));break;case 4:r=El,a=Nl,El=n.stateNode.containerInfo,Nl=!0,Cl(e,t,n),El=r,Nl=a;break;case 0:case 11:case 14:case 15:wl||sl(2,n,t),wl||sl(4,n,t),Cl(e,t,n);break;case 1:wl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&cl(n,t,r)),Cl(e,t,n);break;case 21:Cl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Cl(e,t,n),wl=r;break;default:Cl(e,t,n)}}function Pl(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Nh(e)}catch(n){uu(t,t.return,n)}}function Ol(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new xl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new xl),t;default:throw Error(i(435,e.tag))}}(e);t.forEach((function(t){var r=mu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function Rl(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(fd(l.type)){El=l.stateNode,Nl=!1;break e}break;case 5:El=l.stateNode,Nl=!1;break e;case 3:case 4:El=l.stateNode.containerInfo,Nl=!0;break e}l=l.return}if(null===El)throw Error(i(160));Tl(o,s,a),El=null,Nl=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Al(t,e),t=t.sibling}var Ll=null;function Al(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rl(t,e),Il(e),4&r&&(sl(3,e,e.return),il(3,e),sl(5,e,e.return));break;case 1:Rl(t,e),Il(e),512&r&&(wl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Ll;if(Rl(t,e),Il(e),512&r&&(wl||null===n||dl(n,n.return)),4&r){var o=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Ue]||o[Re]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector("head > title"))),ed(o,r,n),o[Re]=e,Ve(o),r=o;break e;case"link":var s=Ud("link","href",a).get(r+(n.href||""));if(s)for(var l=0;l<s.length;l++)if((o=s[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;case"meta":if(s=Ud("meta","content",a).get(r+(n.content||"")))for(l=0;l<s.length;l++)if((o=s[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;default:throw Error(i(468,r))}o[Re]=e,Ve(o),r=o}e.stateNode=r}else Fd(a,e.type,e.stateNode);else e.stateNode=Ad(a,r,e.memoizedProps);else o!==r?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===r?Fd(a,e.type,e.stateNode):Ad(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&fl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rl(t,e),Il(e),512&r&&(wl||null===n||dl(n,n.return)),null!==n&&4&r&&fl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rl(t,e),Il(e),512&r&&(wl||null===n||dl(n,n.return)),32&e.flags){a=e.stateNode;try{xt(a,"")}catch(p){uu(e,e.return,p)}}4&r&&null!=e.stateNode&&fl(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(kl=!0);break;case 6:if(Rl(t,e),Il(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){uu(e,e.return,p)}}break;case 3:if(Md=null,a=Ll,Ll=_d(t.containerInfo),Rl(t,e),Ll=a,Il(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Nh(t.containerInfo)}catch(p){uu(e,e.return,p)}kl&&(kl=!1,zl(e));break;case 4:r=Ll,Ll=_d(e.stateNode.containerInfo),Rl(t,e),Il(e),Ll=r;break;case 12:default:Rl(t,e),Il(e);break;case 13:Rl(t,e),Il(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(kc=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ol(e,r)));break;case 22:a=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=bl,d=wl;if(bl=u||a,wl=d||c,Rl(t,e),wl=d,bl=u,Il(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||c||bl||wl||Ml(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(o=c.stateNode,a)"function"===typeof(s=o.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=c.stateNode;var h=c.memoizedProps.style,f=void 0!==h&&null!==h&&h.hasOwnProperty("display")?h.display:null;l.style.display=null==f||"boolean"===typeof f?"":(""+f).trim()}}catch(p){uu(c,c.return,p)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=a?"":c.memoizedProps}catch(p){uu(c,c.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Ol(e,n))));break;case 19:Rl(t,e),Il(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ol(e,r)));case 30:case 21:}}function Il(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(pl(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;vl(e,ml(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(xt(o,""),n.flags&=-33),vl(e,ml(e),o);break;case 3:case 4:var s=n.stateNode.containerInfo;gl(e,ml(e),s);break;default:throw Error(i(161))}}catch(l){uu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function zl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;zl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Dl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)_l(e,t.alternate,t),t=t.sibling}function Ml(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),Ml(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&cl(t,t.return,n),Ml(t);break;case 27:kd(t.stateNode);case 26:case 5:dl(t,t.return),Ml(t);break;case 22:null===t.memoizedState&&Ml(t);break;default:Ml(t)}e=e.sibling}}function Ul(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,o=t,i=o.flags;switch(o.tag){case 0:case 11:case 15:Ul(a,o,n),il(4,o);break;case 1:if(Ul(a,o,n),"function"===typeof(a=(r=o).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(a=(r=o).updateQueue)){var s=r.stateNode;try{var l=a.shared.hiddenCallbacks;if(null!==l)for(a.shared.hiddenCallbacks=null,a=0;a<l.length;a++)ho(l[a],s)}catch(c){uu(r,r.return,c)}}n&&64&i&&ll(o),ul(o,o.return);break;case 27:yl(o);case 26:case 5:Ul(a,o,n),n&&null===r&&4&i&&hl(o),ul(o,o.return);break;case 12:Ul(a,o,n);break;case 13:Ul(a,o,n),n&&4&i&&Pl(a,o);break;case 22:null===o.memoizedState&&Ul(a,o,n),ul(o,o.return);break;case 30:break;default:Ul(a,o,n)}t=t.sibling}}function Fl(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Aa(n))}function Bl(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e))}function Hl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)ql(e,t,n,r),t=t.sibling}function ql(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Hl(e,t,n,r),2048&a&&il(9,t);break;case 1:case 13:default:Hl(e,t,n,r);break;case 3:Hl(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e)));break;case 12:if(2048&a){Hl(e,t,n,r),e=t.stateNode;try{var o=t.memoizedProps,i=o.id,s=o.onPostCommit;"function"===typeof s&&s(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){uu(t,t.return,l)}}else Hl(e,t,n,r);break;case 23:break;case 22:o=t.stateNode,i=t.alternate,null!==t.memoizedState?2&o._visibility?Hl(e,t,n,r):Vl(e,t):2&o._visibility?Hl(e,t,n,r):(o._visibility|=2,Wl(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Fl(i,t);break;case 24:Hl(e,t,n,r),2048&a&&Bl(t.alternate,t)}}function Wl(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var o=e,i=t,s=n,l=r,c=i.flags;switch(i.tag){case 0:case 11:case 15:Wl(o,i,s,l,a),il(8,i);break;case 23:break;case 22:var u=i.stateNode;null!==i.memoizedState?2&u._visibility?Wl(o,i,s,l,a):Vl(o,i):(u._visibility|=2,Wl(o,i,s,l,a)),a&&2048&c&&Fl(i.alternate,i);break;case 24:Wl(o,i,s,l,a),a&&2048&c&&Bl(i.alternate,i);break;default:Wl(o,i,s,l,a)}t=t.sibling}}function Vl(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Vl(n,r),2048&a&&Fl(r.alternate,r);break;case 24:Vl(n,r),2048&a&&Bl(r.alternate,r);break;default:Vl(n,r)}t=t.sibling}}var $l=8192;function Kl(e){if(e.subtreeFlags&$l)for(e=e.child;null!==e;)Gl(e),e=e.sibling}function Gl(e){switch(e.tag){case 26:Kl(e),e.flags&$l&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(i(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Td(n.href),o=e.querySelector(Pd(a));if(o)return null!==(e=o._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,void Ve(o);o=e.ownerDocument||e,n=Od(n),(a=xd.get(a))&&zd(n,a),Ve(o=o.createElement("link"));var s=o;s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),ed(o,"link",n),t.instance=o}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ll,e.memoizedState,e.memoizedProps);break;case 5:default:Kl(e);break;case 3:case 4:var t=Ll;Ll=_d(e.stateNode.containerInfo),Kl(e),Ll=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=$l,$l=16777216,Kl(e),$l=t):Kl(e))}}function Jl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ql(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Jl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Yl(e),e=e.sibling}function Yl(e){switch(e.tag){case 0:case 11:case 15:Ql(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:Ql(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Xl(e)):Ql(e)}}function Xl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Jl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:sl(8,t,t.return),Xl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Xl(t));break;default:Xl(t)}e=e.sibling}}function Zl(e,t){for(;null!==Sl;){var n=Sl;switch(n.tag){case 0:case 11:case 15:sl(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Sl=r;else e:for(n=e;null!==Sl;){var a=(r=Sl).sibling,o=r.return;if(jl(r),r===n){Sl=null;break e}if(null!==a){a.return=o,Sl=a;break e}Sl=o}}}var ec={getCacheForType:function(e){var t=Ea(Ra),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tc="function"===typeof WeakMap?WeakMap:Map,nc=0,rc=null,ac=null,oc=0,ic=0,sc=null,lc=!1,cc=!1,uc=!1,dc=0,hc=0,fc=0,pc=0,mc=0,gc=0,vc=0,yc=null,bc=null,wc=!1,kc=0,xc=1/0,Sc=null,_c=null,jc=0,Ec=null,Nc=null,Cc=0,Tc=0,Pc=null,Oc=null,Rc=0,Lc=null;function Ac(){if(0!==(2&nc)&&0!==oc)return oc&-oc;if(null!==A.T){return 0!==Da?Da:Tu()}return Pe()}function Ic(){0===gc&&(gc=0===(536870912&oc)||oa?xe():536870912);var e=as.current;return null!==e&&(e.flags|=32),gc}function zc(e,t,n){(e!==rc||2!==ic&&9!==ic)&&null===e.cancelPendingCommit||(qc(e,0),Fc(e,oc,gc,!1)),je(e,n),0!==(2&nc)&&e===rc||(e===rc&&(0===(2&nc)&&(pc|=n),4===hc&&Fc(e,oc,gc,!1)),xu(e))}function Dc(e,t,n){if(0!==(6&nc))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nc;nc|=2;var r=Vc(),a=$c();rc!==e||oc!==t?(Sc=null,xc=te()+500,qc(e,t)):cc=we(e,t);e:for(;;)try{if(0!==ic&&null!==ac){t=ac;var o=sc;t:switch(ic){case 1:ic=0,sc=null,Zc(e,t,o,1);break;case 2:case 9:if(Ja(o)){ic=0,sc=null,Xc(t);break}t=function(){2!==ic&&9!==ic||rc!==e||(ic=7),xu(e)},o.then(t,t);break e;case 3:ic=7;break e;case 4:ic=5;break e;case 7:Ja(o)?(ic=0,sc=null,Xc(t)):(ic=0,sc=null,Zc(e,t,o,7));break;case 5:var s=null;switch(ac.tag){case 26:s=ac.memoizedState;case 5:case 27:var l=ac;if(!s||Bd(s)){ic=0,sc=null;var c=l.sibling;if(null!==c)ac=c;else{var u=l.return;null!==u?(ac=u,eu(u)):ac=null}break t}}ic=0,sc=null,Zc(e,t,o,5);break;case 6:ic=0,sc=null,Zc(e,t,o,6);break;case 8:Hc(),hc=6;break e;default:throw Error(i(462))}}Qc();break}catch(d){Wc(e,d)}return ya=va=null,A.H=r,A.A=a,nc=n,null!==ac?0:(rc=null,oc=0,Cr(),hc)}(e,t):Gc(e,t,!0),o=r;;){if(0===a){cc&&!r&&Fc(e,t,0,!1);break}if(n=e.current.alternate,!o||Uc(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){t=s;e:{var l=e;a=yc;var c=l.current.memoizedState.isDehydrated;if(c&&(qc(l,s).flags|=256),2!==(s=Gc(l,s,!1))){if(uc&&!c){l.errorRecoveryDisabledLanes|=o,pc|=o,a=4;break e}o=bc,bc=a,null!==o&&(null===bc?bc=o:bc.push.apply(bc,o))}a=s}if(o=!1,2!==a)continue}}if(1===a){qc(e,0),Fc(e,t,0,!0);break}e:{switch(r=e,o=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Fc(r,t,gc,!lc);break e;case 2:bc=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=kc+300-te())){if(Fc(r,t,gc,!lc),0!==be(r,0,!0))break e;r.timeoutHandle=ld(Mc.bind(null,r,n,bc,Sc,wc,t,gc,pc,vc,lc,o,2,-0,0),a)}else Mc(r,n,bc,Sc,wc,t,gc,pc,vc,lc,o,0,-0,0)}break}a=Gc(e,t,!1),o=!1}xu(e)}function Mc(e,t,n,r,a,o,s,l,c,u,d,h,f,p){if(e.timeoutHandle=-1,(8192&(h=t.subtreeFlags)||16785408===(16785408&h))&&(Hd={stylesheets:null,count:0,unsuspend:qd},Gl(t),null!==(h=function(){if(null===Hd)throw Error(i(475));var e=Hd;return e.stylesheets&&0===e.count&&$d(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&$d(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=h(nu.bind(null,e,t,o,n,r,a,s,l,c,d,1,f,p)),void Fc(e,o,s,!u);nu(e,t,o,n,r,a,s,l,c)}function Uc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!Jn(o(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fc(e,t,n,r){t&=~mc,t&=~pc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var o=31-fe(a),i=1<<o;r[o]=-1,a&=~i}0!==n&&Ee(e,n,t)}function Bc(){return 0!==(6&nc)||(Su(0,!1),!1)}function Hc(){if(null!==ac){if(0===ic)var e=ac.return;else ya=va=null,Mo(e=ac),Ji=null,Qi=0,e=ac;for(;null!==e;)ol(e.alternate,e),e=e.return;ac=null}}function qc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hc(),rc=e,ac=n=Mr(e.current,null),oc=t,ic=0,sc=null,lc=!1,cc=we(e,t),uc=!1,vc=gc=mc=pc=fc=hc=0,bc=yc=null,wc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-fe(r),o=1<<a;t|=e[a],r&=~o}return dc=t,Cr(),n}function Wc(e,t){wo=null,A.H=Vi,t===Va||t===Ka?(t=Za(),ic=3):t===$a?(t=Za(),ic=4):ic=t===Es?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,sc=t,null===ac&&(hc=1,ks(e,_r(t,e.current)))}function Vc(){var e=A.H;return A.H=Vi,null===e?Vi:e}function $c(){var e=A.A;return A.A=ec,e}function Kc(){hc=4,lc||(4194048&oc)!==oc&&null!==as.current||(cc=!0),0===(134217727&fc)&&0===(134217727&pc)||null===rc||Fc(rc,oc,gc,!1)}function Gc(e,t,n){var r=nc;nc|=2;var a=Vc(),o=$c();rc===e&&oc===t||(Sc=null,qc(e,t)),t=!1;var i=hc;e:for(;;)try{if(0!==ic&&null!==ac){var s=ac,l=sc;switch(ic){case 8:Hc(),i=6;break e;case 3:case 2:case 9:case 6:null===as.current&&(t=!0);var c=ic;if(ic=0,sc=null,Zc(e,s,l,c),n&&cc){i=0;break e}break;default:c=ic,ic=0,sc=null,Zc(e,s,l,c)}}Jc(),i=hc;break}catch(u){Wc(e,u)}return t&&e.shellSuspendCounter++,ya=va=null,nc=r,A.H=a,A.A=o,null===ac&&(rc=null,oc=0,Cr()),i}function Jc(){for(;null!==ac;)Yc(ac)}function Qc(){for(;null!==ac&&!Z();)Yc(ac)}function Yc(e){var t=Ys(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Xc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zs(n,t,t.pendingProps,t.type,void 0,oc);break;case 11:t=zs(n,t,t.pendingProps,t.type.render,t.ref,oc);break;case 5:Mo(t);default:ol(n,t),t=Ys(n,t=ac=Ur(t,dc),dc)}e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Zc(e,t,n,r){ya=va=null,Mo(t),Ji=null,Qi=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=as.current)){switch(n.tag){case 13:return null===os?Kc():null===n.alternate&&0===hc&&(hc=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ga?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),du(e,r,a)),!1;case 22:return n.flags|=65536,r===Ga?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),du(e,r,a)),!1}throw Error(i(435,n.tag))}return du(e,r,a),Kc(),!1}if(oa)return null!==(t=as.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==la&&ma(_r(e=Error(i(422),{cause:r}),n))):(r!==la&&ma(_r(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=_r(r,n),so(e,a=Ss(e.stateNode,r,a)),4!==hc&&(hc=2)),!1;var o=Error(i(520),{cause:r});if(o=_r(o,n),null===yc?yc=[o]:yc.push(o),4!==hc&&(hc=2),null===t)return!0;r=_r(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,so(n,e=Ss(n.stateNode,r,e)),!1;case 1:if(t=n.type,o=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==o&&"function"===typeof o.componentDidCatch&&(null===_c||!_c.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,js(a=_s(a),e,n,r),so(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,oc))return hc=1,ks(e,_r(n,e.current)),void(ac=null)}catch(o){if(null!==a)throw ac=a,o;return hc=1,ks(e,_r(n,e.current)),void(ac=null)}32768&t.flags?(oa||1===r?e=!0:cc||0!==(536870912&oc)?e=!1:(lc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=as.current)&&13===r.tag&&(r.flags|=16384))),tu(t,e)):eu(t)}function eu(e){var t=e;do{if(0!==(32768&t.flags))return void tu(t,lc);e=t.return;var n=rl(t.alternate,t,dc);if(null!==n)return void(ac=n);if(null!==(t=t.sibling))return void(ac=t);ac=t=e}while(null!==t);0===hc&&(hc=5)}function tu(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(ac=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ac=e);ac=e=n}while(null!==e);hc=6,ac=null}function nu(e,t,n,r,a,o,s,l,c){e.cancelPendingCommit=null;do{su()}while(0!==jc);if(0!==(6&nc))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,a,o){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,c=e.hiddenUpdates;for(n=i&~n;0<n;){var u=31-fe(n),d=1<<u;s[u]=0,l[u]=-1;var h=c[u];if(null!==h)for(c[u]=null,u=0;u<h.length;u++){var f=h[u];null!==f&&(f.lane&=-536870913)}n&=~d}0!==r&&Ee(e,r,0),0!==o&&0===a&&0!==e.tag&&(e.suspendedLanes|=o&~(i&~t))}(e,n,o|=Nr,s,l,c),e===rc&&(ac=rc=null,oc=0),Nc=t,Ec=e,Cc=n,Tc=o,Pc=a,Oc=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,Y(oe,(function(){return lu(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=A.T,A.T=null,a=I.p,I.p=2,s=nc,nc|=4;try{!function(e,t){if(e=e.containerInfo,td=nh,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(g){n=null;break e}var s=0,l=-1,c=-1,u=0,d=0,h=e,f=null;t:for(;;){for(var p;h!==n||0!==a&&3!==h.nodeType||(l=s+a),h!==o||0!==r&&3!==h.nodeType||(c=s+r),3===h.nodeType&&(s+=h.nodeValue.length),null!==(p=h.firstChild);)f=h,h=p;for(;;){if(h===e)break t;if(f===n&&++u===a&&(l=s),f===o&&++d===r&&(c=s),null!==(p=h.nextSibling))break;f=(h=f).parentNode}h=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nh=!1,Sl=t;null!==Sl;)if(e=(t=Sl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Sl=e;else for(;null!==Sl;){switch(o=(t=Sl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var m=gs(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){uu(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Sl=e;break}Sl=t.return}}(e,t)}finally{nc=s,I.p=a,A.T=r}}jc=1,ru(),au(),ou()}}function ru(){if(1===jc){jc=0;var e=Ec,t=Nc,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=A.T,A.T=null;var r=I.p;I.p=2;var a=nc;nc|=4;try{Al(t,e);var o=nd,i=er(e.containerInfo),s=o.focusedElem,l=o.selectionRange;if(i!==s&&s&&s.ownerDocument&&Zn(s.ownerDocument.documentElement,s)){if(null!==l&&tr(s)){var c=l.start,u=l.end;if(void 0===u&&(u=c),"selectionStart"in s)s.selectionStart=c,s.selectionEnd=Math.min(u,s.value.length);else{var d=s.ownerDocument||document,h=d&&d.defaultView||window;if(h.getSelection){var f=h.getSelection(),p=s.textContent.length,m=Math.min(l.start,p),g=void 0===l.end?m:Math.min(l.end,p);!f.extend&&m>g&&(i=g,g=m,m=i);var v=Xn(s,m),y=Xn(s,g);if(v&&y&&(1!==f.rangeCount||f.anchorNode!==v.node||f.anchorOffset!==v.offset||f.focusNode!==y.node||f.focusOffset!==y.offset)){var b=d.createRange();b.setStart(v.node,v.offset),f.removeAllRanges(),m>g?(f.addRange(b),f.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),f.addRange(b))}}}}for(d=[],f=s;f=f.parentNode;)1===f.nodeType&&d.push({element:f,left:f.scrollLeft,top:f.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<d.length;s++){var w=d[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nh=!!td,nd=td=null}finally{nc=a,I.p=r,A.T=n}}e.current=t,jc=2}}function au(){if(2===jc){jc=0;var e=Ec,t=Nc,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=A.T,A.T=null;var r=I.p;I.p=2;var a=nc;nc|=4;try{_l(e,t.alternate,t)}finally{nc=a,I.p=r,A.T=n}}jc=3}}function ou(){if(4===jc||3===jc){jc=0,ee();var e=Ec,t=Nc,n=Cc,r=Oc;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?jc=5:(jc=0,Nc=Ec=null,iu(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(_c=null),Te(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=A.T,a=I.p,I.p=2,A.T=null;try{for(var o=e.onRecoverableError,i=0;i<r.length;i++){var s=r[i];o(s.value,{componentStack:s.stack})}}finally{A.T=t,I.p=a}}0!==(3&Cc)&&su(),xu(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Lc?Rc++:(Rc=0,Lc=e):Rc=0,Su(0,!1)}}function iu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Aa(t)))}function su(e){return ru(),au(),ou(),lu()}function lu(){if(5!==jc)return!1;var e=Ec,t=Tc;Tc=0;var n=Te(Cc),r=A.T,a=I.p;try{I.p=32>n?32:n,A.T=null,n=Pc,Pc=null;var o=Ec,s=Cc;if(jc=0,Nc=Ec=null,Cc=0,0!==(6&nc))throw Error(i(331));var l=nc;if(nc|=4,Yl(o.current),ql(o,o.current,s,n),nc=l,Su(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,o)}catch(c){}return!0}finally{I.p=a,A.T=r,iu(e,t)}}function cu(e,t,n){t=_r(n,t),null!==(e=oo(e,t=Ss(e.stateNode,t,2),2))&&(je(e,2),xu(e))}function uu(e,t,n){if(3===e.tag)cu(e,e,n);else for(;null!==t;){if(3===t.tag){cu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===_c||!_c.has(r))){e=_r(n,e),null!==(r=oo(t,n=_s(2),2))&&(js(n,r,t,e),je(r,2),xu(r));break}}t=t.return}}function du(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(uc=!0,a.add(n),e=hu.bind(null,e,t,n),t.then(e,e))}function hu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(oc&n)===n&&(4===hc||3===hc&&(62914560&oc)===oc&&300>te()-kc?0===(2&nc)&&qc(e,0):mc|=n,vc===oc&&(vc=0)),xu(e)}function fu(e,t){0===t&&(t=Se()),null!==(e=Or(e,t))&&(je(e,t),xu(e))}function pu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),fu(e,n)}function mu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),fu(e,n)}var gu=null,vu=null,yu=!1,bu=!1,wu=!1,ku=0;function xu(e){e!==vu&&null===e.next&&(null===vu?gu=vu=e:vu=vu.next=e),bu=!0,yu||(yu=!0,dd((function(){0!==(6&nc)?Y(re,_u):ju()})))}function Su(e,t){if(!wu&&bu){wu=!0;do{for(var n=!1,r=gu;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var o=0;else{var i=r.suspendedLanes,s=r.pingedLanes;o=(1<<31-fe(42|e)+1)-1,o=201326741&(o&=a&~(i&~s))?201326741&o|1:o?2|o:0}0!==o&&(n=!0,Cu(r,o))}else o=oc,0===(3&(o=be(r,r===rc?o:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,o)||(n=!0,Cu(r,o));r=r.next}}while(n);wu=!1}}function _u(){ju()}function ju(){bu=yu=!1;var e=0;0!==ku&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==sd&&(sd=e,!0);return sd=null,!1}()&&(e=ku),ku=0);for(var t=te(),n=null,r=gu;null!==r;){var a=r.next,o=Eu(r,t);0===o?(r.next=null,null===n?gu=a:n.next=a,null===a&&(vu=n)):(n=r,(0!==e||0!==(3&o))&&(bu=!0)),r=a}Su(e,!1)}function Eu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=-62914561&e.pendingLanes;0<o;){var i=31-fe(o),s=1<<i,l=a[i];-1===l?0!==(s&n)&&0===(s&r)||(a[i]=ke(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}if(n=oc,n=be(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ic||9===ic)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),Te(n)){case 2:case 8:n=ae;break;case 32:default:n=oe;break;case 268435456:n=se}return r=Nu.bind(null,e),n=Y(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function Nu(e,t){if(0!==jc&&5!==jc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(su()&&e.callbackNode!==n)return null;var r=oc;return 0===(r=be(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Dc(e,r,t),Eu(e,te()),null!=e.callbackNode&&e.callbackNode===n?Nu.bind(null,e):null)}function Cu(e,t){if(su())return null;Dc(e,t,!0)}function Tu(){return 0===ku&&(ku=xe()),ku}function Pu(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Tt(""+e)}function Ou(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ru=0;Ru<kr.length;Ru++){var Lu=kr[Ru];xr(Lu.toLowerCase(),"on"+(Lu[0].toUpperCase()+Lu.slice(1)))}xr(fr,"onAnimationEnd"),xr(pr,"onAnimationIteration"),xr(mr,"onAnimationStart"),xr("dblclick","onDoubleClick"),xr("focusin","onFocus"),xr("focusout","onBlur"),xr(gr,"onTransitionRun"),xr(vr,"onTransitionStart"),xr(yr,"onTransitionCancel"),xr(br,"onTransitionEnd"),Je("onMouseEnter",["mouseout","mouseover"]),Je("onMouseLeave",["mouseout","mouseover"]),Je("onPointerEnter",["pointerout","pointerover"]),Je("onPointerLeave",["pointerout","pointerover"]),Ge("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ge("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ge("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ge("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Au="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Iu=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Au));function zu(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=c;try{o(a)}catch(u){vs(u)}a.currentTarget=null,o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,c=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=c;try{o(a)}catch(u){vs(u)}a.currentTarget=null,o=l}}}}function Du(e,t){var n=t[Ie];void 0===n&&(n=t[Ie]=new Set);var r=e+"__bubble";n.has(r)||(Bu(t,e,2,!1),n.add(r))}function Mu(e,t,n){var r=0;t&&(r|=4),Bu(n,e,r,t)}var Uu="_reactListening"+Math.random().toString(36).slice(2);function Fu(e){if(!e[Uu]){e[Uu]=!0,$e.forEach((function(t){"selectionchange"!==t&&(Iu.has(t)||Mu(t,!1,e),Mu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Uu]||(t[Uu]=!0,Mu("selectionchange",!1,t))}}function Bu(e,t,n,r){switch(ch(t)){case 2:var a=rh;break;case 8:a=ah;break;default:a=oh}n=a.bind(null,t,n,e),a=void 0,!Ut||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hu(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==s;){if(null===(i=Be(s)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=o=i;continue e}s=s.parentNode}}r=r.return}zt((function(){var r=o,a=Ot(n),i=[];e:{var s=wr.get(e);if(void 0!==s){var c=Zt,u=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":c=mn;break;case"focusin":u="focus",c=on;break;case"focusout":u="blur",c=on;break;case"beforeblur":case"afterblur":c=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=vn;break;case fr:case pr:case mr:c=sn;break;case br:c=yn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=bn;break;case"copy":case"cut":case"paste":c=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=wn}var d=0!==(4&t),h=!d&&("scroll"===e||"scrollend"===e),f=d?null!==s?s+"Capture":null:s;d=[];for(var p,m=r;null!==m;){var g=m;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===f||null!=(g=Dt(m,f))&&d.push(qu(m,g,p)),h)break;m=m.return}0<d.length&&(s=new c(s,u,null,n,a),i.push({event:s,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Pt||!(u=n.relatedTarget||n.fromElement)||!Be(u)&&!u[Ae])&&(c||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?Be(u):null)&&(h=l(u),d=u.tag,u!==h||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=rn,g="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",f="onPointerEnter",m="pointer"),h=null==c?s:qe(c),p=null==u?s:qe(u),(s=new d(g,m+"leave",c,n,a)).target=h,s.relatedTarget=p,g=null,Be(a)===r&&((d=new d(f,m+"enter",u,n,a)).target=p,d.relatedTarget=h,g=d),h=g,c&&u)e:{for(f=u,m=0,p=d=c;p;p=Vu(p))m++;for(p=0,g=f;g;g=Vu(g))p++;for(;0<m-p;)d=Vu(d),m--;for(;0<p-m;)f=Vu(f),p--;for(;m--;){if(d===f||null!==f&&d===f.alternate)break e;d=Vu(d),f=Vu(f)}d=null}else d=null;null!==c&&$u(i,s,c,d,!1),null!==u&&null!==h&&$u(i,h,u,d,!0)}if("select"===(c=(s=r?qe(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===c&&"file"===s.type)var v=Mn;else if(Rn(s))if(Un)v=Gn;else{v=$n;var y=Vn}else!(c=s.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Et(r.elementType)&&(v=Mn):v=Kn;switch(v&&(v=v(e,r))?Ln(i,v,n,a):(y&&y(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&yt(s,"number",s.value)),y=r?qe(r):window,e){case"focusin":(Rn(y)||"true"===y.contentEditable)&&(rr=y,ar=r,or=null);break;case"focusout":or=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,sr(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":sr(i,n,a)}var b;if(xn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Pn?Cn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(jn&&"ko"!==n.locale&&(Pn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Pn&&(b=Wt()):(Ht="value"in(Bt=a)?Bt.value:Bt.textContent,Pn=!0)),0<(y=Wu(r,w)).length&&(w=new cn(w,e,null,n,a),i.push({event:w,listeners:y}),b?w.data=b:null!==(b=Tn(n))&&(w.data=b))),(b=_n?function(e,t){switch(e){case"compositionend":return Tn(t);case"keypress":return 32!==t.which?null:(Nn=!0,En);case"textInput":return(e=t.data)===En&&Nn?null:e;default:return null}}(e,n):function(e,t){if(Pn)return"compositionend"===e||!xn&&Cn(e,t)?(e=Wt(),qt=Ht=Bt=null,Pn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return jn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Wu(r,"onBeforeInput")).length&&(y=new cn("onBeforeInput","beforeinput",null,n,a),i.push({event:y,listeners:w}),y.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var o=Pu((a[Le]||null).action),i=r.submitter;i&&null!==(t=(t=i[Le]||null)?Pu(t.formAction):i.getAttribute("formAction"))&&(o=t,i=null);var s=new Zt("action","action",null,r,a);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==ku){var e=i?Ou(a,i):new FormData(a);Oi(n,{pending:!0,data:e,method:a.method,action:o},null,e)}}else"function"===typeof o&&(s.preventDefault(),e=i?Ou(a,i):new FormData(a),Oi(n,{pending:!0,data:e,method:a.method,action:o},o,e))},currentTarget:a}]})}}(i,e,r,n,a)}zu(i,t)}))}function qu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wu(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===o||(null!=(a=Dt(e,n))&&r.unshift(qu(e,a,o)),null!=(a=Dt(e,t))&&r.push(qu(e,a,o))),3===e.tag)return r;e=e.return}return[]}function Vu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function $u(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,c=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===c||(l=c,a?null!=(c=Dt(n,o))&&i.unshift(qu(n,c,l)):a||null!=(c=Dt(n,o))&&i.push(qu(n,c,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Ku=/\r\n?/g,Gu=/\u0000|\uFFFD/g;function Ju(e){return("string"===typeof e?e:""+e).replace(Ku,"\n").replace(Gu,"")}function Qu(e,t){return t=Ju(t),Ju(e)===t}function Yu(){}function Xu(e,t,n,r,a,o){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||xt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&xt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":jt(e,r,o);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof o&&("formAction"===n?("input"!==t&&Xu(e,t,"name",a.name,a,null),Xu(e,t,"formEncType",a.formEncType,a,null),Xu(e,t,"formMethod",a.formMethod,a,null),Xu(e,t,"formTarget",a.formTarget,a,null)):(Xu(e,t,"encType",a.encType,a,null),Xu(e,t,"method",a.method,a,null),Xu(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"onScroll":null!=r&&Du("scroll",e);break;case"onScrollEnd":null!=r&&Du("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Tt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Du("beforetoggle",e),Du("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Nt.get(n)||n,r)}}function Zu(e,t,n,r,a,o){switch(n){case"style":jt(e,r,o);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?xt(e,r):("number"===typeof r||"bigint"===typeof r)&&xt(e,""+r);break;case"onScroll":null!=r&&Du("scroll",e);break;case"onScrollEnd":null!=r&&Du("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(o=null!=(o=e[Le]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Du("error",e),Du("load",e);var r,a=!1,o=!1;for(r in n)if(n.hasOwnProperty(r)){var s=n[r];if(null!=s)switch(r){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xu(e,t,r,s,n,null)}}return o&&Xu(e,t,"srcSet",n.srcSet,n,null),void(a&&Xu(e,t,"src",n.src,n,null));case"input":Du("invalid",e);var l=r=s=o=null,c=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":s=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Xu(e,t,a,d,n,null)}}return vt(e,r,l,c,u,s,o,!1),void dt(e);case"select":for(o in Du("invalid",e),a=s=r=null,n)if(n.hasOwnProperty(o)&&null!=(l=n[o]))switch(o){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":a=l;default:Xu(e,t,o,l,n,null)}return t=r,n=s,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(s in Du("invalid",e),r=o=a=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":a=l;break;case"defaultValue":o=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(i(91));break;default:Xu(e,t,s,l,n,null)}return kt(e,a,o,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))if("selected"===c)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Xu(e,t,c,a,n,null);return;case"dialog":Du("beforetoggle",e),Du("toggle",e),Du("cancel",e),Du("close",e);break;case"iframe":case"object":Du("load",e);break;case"video":case"audio":for(a=0;a<Au.length;a++)Du(Au[a],e);break;case"image":Du("error",e),Du("load",e);break;case"details":Du("toggle",e);break;case"embed":case"source":case"link":Du("error",e),Du("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xu(e,t,u,a,n,null)}return;default:if(Et(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zu(e,t,d,a,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(a=n[l])&&Xu(e,t,l,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var sd=null;var ld="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(hd)}:ld;function hd(e){setTimeout((function(){throw e}))}function fd(e){return"head"===e}function pd(e,t){var n=t,r=0,a=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&kd(i.documentElement),2&n&&kd(i.body),4&n)for(kd(n=i.head),i=n.firstChild;i;){var s=i.nextSibling,l=i.nodeName;i[Ue]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=s}}if(0===a)return e.removeChild(o),void Nh(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=o}while(n);Nh(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Fe(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Fe(e)}var xd=new Map,Sd=new Set;function _d(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var jd=I.d;I.d={f:function(){var e=jd.f(),t=Bc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Li(t):jd.r(e)},D:function(e){jd.D(e),Nd("dns-prefetch",e,null)},C:function(e,t){jd.C(e,t),Nd("preconnect",e,t)},L:function(e,t,n){jd.L(e,t,n);var r=Ed;if(r&&e&&t){var a='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+mt(n.imageSizes)+'"]')):a+='[href="'+mt(e)+'"]';var o=a;switch(t){case"style":o=Td(e);break;case"script":o=Rd(e)}xd.has(o)||(e=h({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),xd.set(o,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Pd(o))||"script"===t&&r.querySelector(Ld(o))||(ed(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){jd.m(e,t);var n=Ed;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',o=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Rd(e)}if(!xd.has(o)&&(e=h({rel:"modulepreload",href:e},t),xd.set(o,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ld(o)))return}ed(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){jd.X(e,t);var n=Ed;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=h({src:e,async:!0},t),(t=xd.get(a))&&Dd(e,t),Ve(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}},S:function(e,t,n){jd.S(e,t,n);var r=Ed;if(r&&e){var a=We(r).hoistableStyles,o=Td(e);t=t||"default";var i=a.get(o);if(!i){var s={loading:0,preload:null};if(i=r.querySelector(Pd(o)))s.loading=5;else{e=h({rel:"stylesheet",href:e,"data-precedence":t},n),(n=xd.get(o))&&zd(e,n);var l=i=r.createElement("link");Ve(l),ed(l,"link",e),l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),l.addEventListener("load",(function(){s.loading|=1})),l.addEventListener("error",(function(){s.loading|=2})),s.loading|=4,Id(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:s},a.set(o,i)}}},M:function(e,t){jd.M(e,t);var n=Ed;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=h({src:e,async:!0,type:"module"},t),(t=xd.get(a))&&Dd(e,t),Ve(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}}};var Ed="undefined"===typeof document?null:document;function Nd(e,t,n){var r=Ed;if(r&&"string"===typeof t&&t){var a=mt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function Cd(e,t,n,r){var a,o,s,l,c=(c=W.current)?_d(c):null;if(!c)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Td(n.href),(r=(n=We(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Td(n.href);var u=We(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Pd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),xd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xd.set(e,n),u||(a=c,o=e,s=n,l=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?l.loading=1:(o=a.createElement("link"),l.preload=o,o.addEventListener("load",(function(){return l.loading|=1})),o.addEventListener("error",(function(){return l.loading|=2})),ed(o,"link",s),Ve(o),a.head.appendChild(o))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Rd(n),(r=(n=We(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Td(e){return'href="'+mt(e)+'"'}function Pd(e){return'link[rel="stylesheet"]['+e+"]"}function Od(e){return h({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+mt(e)+'"]'}function Ld(e){return"script[async]"+e}function Ad(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var a=h({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Id(r,n.precedence,e),t.instance=r;case"stylesheet":a=Td(n.href);var o=e.querySelector(Pd(a));if(o)return t.state.loading|=4,t.instance=o,Ve(o),o;r=Od(n),(a=xd.get(a))&&zd(r,a),Ve(o=(e.ownerDocument||e).createElement("link"));var s=o;return s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),ed(o,"link",r),t.state.loading|=4,Id(o,n.precedence,e),t.instance=o;case"script":return o=Rd(n.src),(a=e.querySelector(Ld(o)))?(t.instance=a,Ve(a),a):(r=n,(a=xd.get(o))&&Dd(r=h({},n),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Id(r,n.precedence,e));return t.instance}function Id(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,o=a,i=0;i<r.length;i++){var s=r[i];if(s.dataset.precedence===t)o=s;else if(o!==a)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Md=null;function Ud(e,t,n){if(null===Md){var r=new Map,a=Md=new Map;a.set(n,r)}else(r=(a=Md).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var o=n[a];if(!(o[Ue]||o[Re]||"link"===e&&"stylesheet"===o.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==o.namespaceURI){var i=o.getAttribute(t)||"";i=e+i;var s=r.get(i);s?s.push(o):r.set(i,[o])}}return r}function Fd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function qd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)$d(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Vd=null;function $d(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Vd=new Map,t.forEach(Kd,e),Vd=null,Wd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Vd.get(e);if(n)var r=n.get(null);else{n=new Map,Vd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<a.length;o++){var i=a[o];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(o=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Wd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),o?o.parentNode.insertBefore(a,o.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gd={$$typeof:k,Provider:null,Consumer:null,_currentValue:z,_currentValue2:z,_threadCount:0};function Jd(e,t,n,r,a,o,i,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=_e(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_e(0),this.hiddenUpdates=_e(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=o,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Qd(e,t,n,r,a,o,i,s,l,c,u,d){return e=new Jd(e,t,n,i,s,l,c,d),t=1,!0===o&&(t|=24),o=zr(3,null,null,t),e.current=o,o.stateNode=e,(t=La()).refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:r,isDehydrated:n,cache:t},no(o),e}function Yd(e){return e?e=Ar:Ar}function Xd(e,t,n,r,a,o){a=Yd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ao(t)).payload={element:n},null!==(o=void 0===o?null:o)&&(r.callback=o),null!==(n=oo(e,r,t))&&(zc(n,0,t),io(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function eh(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function th(e){if(13===e.tag){var t=Or(e,67108864);null!==t&&zc(t,0,67108864),eh(e,67108864)}}var nh=!0;function rh(e,t,n,r){var a=A.T;A.T=null;var o=I.p;try{I.p=2,oh(e,t,n,r)}finally{I.p=o,A.T=a}}function ah(e,t,n,r){var a=A.T;A.T=null;var o=I.p;try{I.p=8,oh(e,t,n,r)}finally{I.p=o,A.T=a}}function oh(e,t,n,r){if(nh){var a=ih(r);if(null===a)Hu(e,t,r,sh,n),yh(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return dh=bh(dh,e,t,n,r,a),!0;case"dragenter":return hh=bh(hh,e,t,n,r,a),!0;case"mouseover":return fh=bh(fh,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return ph.set(o,bh(ph.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,mh.set(o,bh(mh.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(yh(e,r),4&t&&-1<vh.indexOf(e)){for(;null!==a;){var o=He(a);if(null!==o)switch(o.tag){case 3:if((o=o.stateNode).current.memoizedState.isDehydrated){var i=ye(o.pendingLanes);if(0!==i){var s=o;for(s.pendingLanes|=2,s.entangledLanes|=2;i;){var l=1<<31-fe(i);s.entanglements[1]|=l,i&=~l}xu(o),0===(6&nc)&&(xc=te()+500,Su(0,!1))}}break;case 13:null!==(s=Or(o,2))&&zc(s,0,2),Bc(),eh(o,2)}if(null===(o=ih(r))&&Hu(e,t,r,sh,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Hu(e,t,r,null,n)}}function ih(e){return lh(e=Ot(e))}var sh=null;function lh(e){if(sh=null,null!==(e=Be(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sh=e,null}function ch(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case oe:case ie:return 32;case se:return 268435456;default:return 32}default:return 32}}var uh=!1,dh=null,hh=null,fh=null,ph=new Map,mh=new Map,gh=[],vh="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function yh(e,t){switch(e){case"focusin":case"focusout":dh=null;break;case"dragenter":case"dragleave":hh=null;break;case"mouseover":case"mouseout":fh=null;break;case"pointerover":case"pointerout":ph.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":mh.delete(t.pointerId)}}function bh(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&th(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function wh(e){var t=Be(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}(e.priority,(function(){if(13===n.tag){var e=Ac();e=Ce(e);var t=Or(n,e);null!==t&&zc(t,0,e),eh(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kh(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=ih(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&th(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Pt=r,n.target.dispatchEvent(r),Pt=null,t.shift()}return!0}function xh(e,t,n){kh(e)&&n.delete(t)}function Sh(){uh=!1,null!==dh&&kh(dh)&&(dh=null),null!==hh&&kh(hh)&&(hh=null),null!==fh&&kh(fh)&&(fh=null),ph.forEach(xh),mh.forEach(xh)}function _h(e,t){e.blockedOn===t&&(e.blockedOn=null,uh||(uh=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Sh)))}var jh=null;function Eh(e){jh!==e&&(jh=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){jh===e&&(jh=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===lh(r||n))continue;break}var o=He(n);null!==o&&(e.splice(t,3),t-=3,Oi(o,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Nh(e){function t(t){return _h(t,e)}null!==dh&&_h(dh,e),null!==hh&&_h(hh,e),null!==fh&&_h(fh,e),ph.forEach(t),mh.forEach(t);for(var n=0;n<gh.length;n++){var r=gh[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<gh.length&&null===(n=gh[0]).blockedOn;)wh(n),null===n.blockedOn&&gh.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],o=n[r+1],i=a[Le]||null;if("function"===typeof o)i||Eh(n);else if(i){var s=null;if(o&&o.hasAttribute("formAction")){if(a=o,i=o[Le]||null)s=i.formAction;else if(null!==lh(a))continue}else s=i.action;"function"===typeof s?n[r+1]=s:(n.splice(r,3),r-=3),Eh(n)}}}function Ch(e){this._internalRoot=e}function Th(e){this._internalRoot=e}Th.prototype.render=Ch.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Xd(t.current,Ac(),e,t,null,null)},Th.prototype.unmount=Ch.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Xd(e.current,2,null,e,null,null),Bc(),t[Ae]=null}},Th.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gh.length&&0!==t&&t<gh[n].priority;n++);gh.splice(n,0,e),0===n&&wh(e)}};var Ph=a.version;if("19.1.0"!==Ph)throw Error(i(527,Ph,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return u(a),e;if(o===r)return u(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,c=a.child;c;){if(c===n){s=!0,n=a,r=o;break}if(c===r){s=!0,r=a,n=o;break}c=c.sibling}if(!s){for(c=o.child;c;){if(c===n){s=!0,n=o,r=a;break}if(c===r){s=!0,r=o,n=a;break}c=c.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Oh={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Rh=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rh.isDisabled&&Rh.supportsFiber)try{ue=Rh.inject(Oh),de=Rh}catch(Ah){}}t.createRoot=function(e,t){if(!s(e))throw Error(i(299));var n=!1,r="",a=ys,o=bs,l=ws;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Qd(e,1,!1,null,0,n,r,a,o,l,0,null),e[Ae]=t.current,Fu(e),new Ch(t)},t.hydrateRoot=function(e,t,n){if(!s(e))throw Error(i(299));var r=!1,a="",o=ys,l=bs,c=ws,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(o=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Qd(e,1,!0,t,0,r,a,o,l,c,0,u)).context=Yd(null),n=t.current,(a=ao(r=Ce(r=Ac()))).callback=null,oo(n,a,r),n=r,t.current.lanes=n,je(t,n),xu(t),e[Ae]=t.current,Fu(e),new Th(t)},t.version="19.1.0"},43:(e,t,n)=>{e.exports=n(288)},210:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(829)),o=r(n(736)),i=n(745);class s{constructor(e){let{headers:t={},schema:n,fetch:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=Object.assign(Object.assign({},i.DEFAULT_HEADERS),t),this.schemaName=n,this.fetch=r}from(e){const t=new URL("".concat(this.url,"/").concat(e));return new a.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new s(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{head:r=!1,get:a=!1,count:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=new URL("".concat(this.url,"/rpc/").concat(e));let l;r||a?(t=r?"HEAD":"GET",Object.entries(n).filter((e=>{let[t,n]=e;return void 0!==n})).map((e=>{let[t,n]=e;return[t,Array.isArray(n)?"{".concat(n.join(","),"}"):"".concat(n)]})).forEach((e=>{let[t,n]=e;s.searchParams.append(t,n)}))):(t="POST",l=n);const c=Object.assign({},this.headers);return i&&(c.Prefer="count=".concat(i)),new o.default({method:t,url:s,headers:c,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}}t.default=s},286:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(630)),o=r(n(611));t.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"===typeof fetch?this.fetch=a.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let n=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((async e=>{var t,n,r;let a=null,i=null,s=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),o=null===(n=e.headers.get("content-range"))||void 0===n?void 0:n.split("/");r&&o&&o.length>1&&(s=parseInt(o[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(a={code:"PGRST116",details:"Results contain ".concat(i.length," rows, application/vnd.pgrst.object+json requires 1 row"),hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,s=null,l=406,c="Not Acceptable"):i=1===i.length?i[0]:null)}else{const t=await e.text();try{a=JSON.parse(t),Array.isArray(a)&&404===e.status&&(i=[],a=null,l=200,c="OK")}catch(u){404===e.status&&""===t?(l=204,c="No Content"):a={message:t}}if(a&&this.isMaybeSingle&&(null===(r=null===a||void 0===a?void 0:a.details)||void 0===r?void 0:r.includes("0 rows"))&&(a=null,l=200,c="OK"),a&&this.shouldThrowOnError)throw new o.default(a)}return{error:a,data:i,count:s,status:l,statusText:c}}));return this.shouldThrowOnError||(n=n.catch((e=>{var t,n,r;return{error:{message:"".concat(null!==(t=null===e||void 0===e?void 0:e.name)&&void 0!==t?t:"FetchError",": ").concat(null===e||void 0===e?void 0:e.message),details:"".concat(null!==(n=null===e||void 0===e?void 0:e.stack)&&void 0!==n?n:""),hint:"",code:"".concat(null!==(r=null===e||void 0===e?void 0:e.code)&&void 0!==r?r:"")},data:null,count:null,status:0,statusText:""}}))),n.then(e,t)}returns(){return this}overrideTypes(){return this}}},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),f=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,m(w,v.prototype),w.isPureReactComponent=!0;var k=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function _(e,t,r,a,o,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function j(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var E=/\/+/g;function N(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function C(){}function T(e,t,a,o,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,c,u=!1;if(null===e)u=!0;else switch(s){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case h:return T((u=e._init)(e._payload),t,a,o,i)}}if(u)return i=i(e),u=""===o?"."+N(e,0):o,k(i)?(a="",null!=u&&(a=u.replace(E,"$&/")+"/"),T(i,t,a,"",(function(e){return e}))):null!=i&&(j(i)&&(l=i,c=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(E,"$&/")+"/")+u,i=_(l.type,c,void 0,0,0,l.props)),t.push(i)),1;u=0;var d,p=""===o?".":o+":";if(k(e))for(var m=0;m<e.length;m++)u+=T(o=e[m],t,a,s=p+N(o,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=f&&d[f]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(o=e.next()).done;)u+=T(o=o.value,t,a,s=p+N(o,m++),i);else if("object"===s){if("function"===typeof e.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(C,C):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,o,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function P(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function L(){}t.Children={map:P,forEach:function(e,t,n){P(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return P(e,(function(){t++})),t},toArray:function(e){return P(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(o in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,o)||"key"===o||"__self"===o||"__source"===o||"ref"===o&&void 0===t.ref||(r[o]=t[o]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var i=Array(o),s=0;s<o;s++)i[s]=arguments[s+2];r.children=i}return _(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,a={},o=null;if(null!=t)for(r in void 0!==t.key&&(o=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];a.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return _(e,o,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=j,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=x.T,n={};x.T=n;try{var r=e(),a=x.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(L,R)}catch(o){R(o)}finally{x.T=t}},t.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},t.use=function(e){return x.H.use(e)},t.useActionState=function(e,t,n){return x.H.useActionState(e,t,n)},t.useCallback=function(e,t){return x.H.useCallback(e,t)},t.useContext=function(e){return x.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return x.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=x.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return x.H.useId()},t.useImperativeHandle=function(e,t,n){return x.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return x.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return x.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return x.H.useMemo(e,t)},t.useOptimistic=function(e,t){return x.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return x.H.useReducer(e,t,n)},t.useRef=function(e){return x.H.useRef(e)},t.useState=function(e){return x.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return x.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return x.H.useTransition()},t.version="19.1.0"},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,s=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},374:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(286));class o extends a.default{select(e){let t=!1;const n=(null!==e&&void 0!==e?e:"*").split("").map((e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e))).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e){let{ascending:t=!0,nullsFirst:n,foreignTable:r,referencedTable:a=r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=a?"".concat(a,".order"):"order",i=this.url.searchParams.get(o);return this.url.searchParams.set(o,"".concat(i?"".concat(i,","):"").concat(e,".").concat(t?"asc":"desc").concat(void 0===n?"":n?".nullsfirst":".nullslast")),this}limit(e){let{foreignTable:t,referencedTable:n=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r="undefined"===typeof n?"limit":"".concat(n,".limit");return this.url.searchParams.set(r,"".concat(e)),this}range(e,t){let{foreignTable:n,referencedTable:r=n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a="undefined"===typeof r?"offset":"".concat(r,".offset"),o="undefined"===typeof r?"limit":"".concat(r,".limit");return this.url.searchParams.set(a,"".concat(e)),this.url.searchParams.set(o,"".concat(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain(){let{analyze:e=!1,verbose:t=!1,settings:n=!1,buffers:r=!1,wal:a=!1,format:o="text"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var i;const s=[e?"analyze":null,t?"verbose":null,n?"settings":null,r?"buffers":null,a?"wal":null].filter(Boolean).join("|"),l=null!==(i=this.headers.Accept)&&void 0!==i?i:"application/json";return this.headers.Accept="application/vnd.pgrst.plan+".concat(o,'; for="').concat(l,'"; options=').concat(s,";"),this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=o},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},400:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},579:(e,t,n)=>{e.exports=n(799)},611:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class n extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=n},630:(e,t,n)=>{n.r(t),n.d(t,{Headers:()=>i,Request:()=>s,Response:()=>l,default:()=>o,fetch:()=>a});var r=function(){if("undefined"!==typeof self)return self;if("undefined"!==typeof window)return window;if("undefined"!==typeof n.g)return n.g;throw new Error("unable to locate global object")}();const a=r.fetch,o=r.fetch.bind(r),i=r.Headers,s=r.Request,l=r.Response},672:(e,t,n)=>{var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(a(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=i.p;try{if(l.T=null,i.p=2,e)return e()}finally{l.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,o="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},736:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(374));class o extends a.default{eq(e,t){return this.url.searchParams.append(e,"eq.".concat(t)),this}neq(e,t){return this.url.searchParams.append(e,"neq.".concat(t)),this}gt(e,t){return this.url.searchParams.append(e,"gt.".concat(t)),this}gte(e,t){return this.url.searchParams.append(e,"gte.".concat(t)),this}lt(e,t){return this.url.searchParams.append(e,"lt.".concat(t)),this}lte(e,t){return this.url.searchParams.append(e,"lte.".concat(t)),this}like(e,t){return this.url.searchParams.append(e,"like.".concat(t)),this}likeAllOf(e,t){return this.url.searchParams.append(e,"like(all).{".concat(t.join(","),"}")),this}likeAnyOf(e,t){return this.url.searchParams.append(e,"like(any).{".concat(t.join(","),"}")),this}ilike(e,t){return this.url.searchParams.append(e,"ilike.".concat(t)),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,"ilike(all).{".concat(t.join(","),"}")),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,"ilike(any).{".concat(t.join(","),"}")),this}is(e,t){return this.url.searchParams.append(e,"is.".concat(t)),this}in(e,t){const n=Array.from(new Set(t)).map((e=>"string"===typeof e&&new RegExp("[,()]").test(e)?'"'.concat(e,'"'):"".concat(e))).join(",");return this.url.searchParams.append(e,"in.(".concat(n,")")),this}contains(e,t){return"string"===typeof t?this.url.searchParams.append(e,"cs.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cs.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cs.".concat(JSON.stringify(t))),this}containedBy(e,t){return"string"===typeof t?this.url.searchParams.append(e,"cd.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cd.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cd.".concat(JSON.stringify(t))),this}rangeGt(e,t){return this.url.searchParams.append(e,"sr.".concat(t)),this}rangeGte(e,t){return this.url.searchParams.append(e,"nxl.".concat(t)),this}rangeLt(e,t){return this.url.searchParams.append(e,"sl.".concat(t)),this}rangeLte(e,t){return this.url.searchParams.append(e,"nxr.".concat(t)),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,"adj.".concat(t)),this}overlaps(e,t){return"string"===typeof t?this.url.searchParams.append(e,"ov.".concat(t)):this.url.searchParams.append(e,"ov.{".concat(t.join(","),"}")),this}textSearch(e,t){let{config:n,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a="";"plain"===r?a="pl":"phrase"===r?a="ph":"websearch"===r&&(a="w");const o=void 0===n?"":"(".concat(n,")");return this.url.searchParams.append(e,"".concat(a,"fts").concat(o,".").concat(t)),this}match(e){return Object.entries(e).forEach((e=>{let[t,n]=e;this.url.searchParams.append(t,"eq.".concat(n))})),this}not(e,t,n){return this.url.searchParams.append(e,"not.".concat(t,".").concat(n)),this}or(e){let{foreignTable:t,referencedTable:n=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=n?"".concat(n,".or"):"or";return this.url.searchParams.append(r,"(".concat(e,")")),this}filter(e,t,n){return this.url.searchParams.append(e,"".concat(t,".").concat(n)),this}}t.default=o},745:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;const r=n(400);t.DEFAULT_HEADERS={"X-Client-Info":"postgrest-js/".concat(r.version)}},799:(e,t)=>{var n=Symbol.for("react.transitional.element");function r(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}Symbol.for("react.fragment"),t.jsx=r,t.jsxs=r},829:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(736));t.default=class{constructor(e,t){let{headers:n={},schema:r,fetch:a}=t;this.url=e,this.headers=n,this.schema=r,this.fetch=a}select(e){let{head:t=!1,count:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t?"HEAD":"GET";let o=!1;const i=(null!==e&&void 0!==e?e:"*").split("").map((e=>/\s/.test(e)&&!o?"":('"'===e&&(o=!o),e))).join("");return this.url.searchParams.set("select",i),n&&(this.headers.Prefer="count=".concat(n)),new a.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e){let{count:t,defaultToNull:n=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push("count=".concat(t)),n||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>'"'.concat(e,'"')));this.url.searchParams.set("columns",e.join(","))}}return new a.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e){let{onConflict:t,ignoreDuplicates:n=!1,count:r,defaultToNull:o=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=["resolution=".concat(n?"ignore":"merge","-duplicates")];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push("count=".concat(r)),o||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>'"'.concat(e,'"')));this.url.searchParams.set("columns",e.join(","))}}return new a.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e){let{count:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push("count=".concat(t)),this.headers.Prefer=n.join(","),new a.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete(){let{count:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[];return e&&t.push("count=".concat(e)),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new a.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var s=2*(r+1)-1,l=e[s],c=s+1,u=e[c];if(0>o(l,n))c<a&&0>o(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[s]=n,r=s);else{if(!(c<a&&0>o(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var c=[],u=[],d=1,h=null,f=3,p=!1,m=!1,g=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(g=!1,k(e),!m)if(null!==r(c))m=!0,_||(_=!0,S());else{var t=r(u);null!==t&&R(x,t.startTime-e)}}var S,_=!1,j=-1,E=5,N=-1;function C(){return!!v||!(t.unstable_now()-N<E)}function T(){if(v=!1,_){var e=t.unstable_now();N=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(j),j=-1),p=!0;var o=f;try{t:{for(k(e),h=r(c);null!==h&&!(h.expirationTime>e&&C());){var i=h.callback;if("function"===typeof i){h.callback=null,f=h.priorityLevel;var s=i(h.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof s){h.callback=s,k(e),n=!0;break t}h===r(c)&&a(c),k(e)}else a(c);h=r(c)}if(null!==h)n=!0;else{var l=r(u);null!==l&&R(x,l.startTime-e),n=!1}}break e}finally{h=null,f=o,p=!1}n=void 0}}finally{n?S():_=!1}}}if("function"===typeof w)S=function(){w(T)};else if("undefined"!==typeof MessageChannel){var P=new MessageChannel,O=P.port2;P.port1.onmessage=T,S=function(){O.postMessage(null)}}else S=function(){y(T,0)};function R(e,n){j=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_requestPaint=function(){v=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(e.sortIndex=o,n(u,e),null===r(c)&&e===r(u)&&(g?(b(j),j=-1):g=!0,R(x,o-i))):(e.sortIndex=s,n(c,e),m||p||(m=!0,_||(_=!0,S()))),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)},952:e=>{e.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},980:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;const a=r(n(210));t.PostgrestClient=a.default;const o=r(n(829));t.PostgrestQueryBuilder=o.default;const i=r(n(736));t.PostgrestFilterBuilder=i.default;const s=r(n(374));t.PostgrestTransformBuilder=s.default;const l=r(n(286));t.PostgrestBuilder=l.default;const c=r(n(611));t.PostgrestError=c.default,t.default={PostgrestClient:a.default,PostgrestQueryBuilder:o.default,PostgrestFilterBuilder:i.default,PostgrestTransformBuilder:s.default,PostgrestBuilder:l.default,PostgrestError:c.default}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".f17adc29.chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="react-version:";n.l=(r,a,o,i)=>{if(e[r])e[r].push(a);else{var s,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[a];var h=(t,n)=>{s.onerror=s.onload=null,clearTimeout(f);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach((e=>e(n))),t)return t(n)},f=setTimeout(h.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=h.bind(null,s.onerror),s.onload=h.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/my-tutor-app/react-version/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=o);var i=n.p+n.u(t),s=new Error;n.l(i,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",s.name="ChunkLoadError",s.type=o,s.request=i,a[1](s)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,o,i=r[0],s=r[1],l=r[2],c=0;if(i.some((t=>0!==e[t]))){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)l(n)}for(t&&t(r);c<i.length;c++)o=i[c],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkreact_version=self.webpackChunkreact_version||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n(43),a=n(391);function o(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function l(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(358);const d=["sri"],h=["page"],f=["page","matches"],p=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],m=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],g=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var v="popstate";function y(){return j((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return x("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:S(t)}),null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function b(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function w(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function k(e,t){return{usr:e.state,key:e.key,idx:t}}function x(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return u(u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?_(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function S(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function _(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function j(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,s="POP",l=null,c=d();function d(){return(i.state||{idx:null}).idx}function h(){s="POP";let e=d(),t=null==e?null:e-c;c=e,l&&l({action:s,location:p.location,delta:t})}function f(e){return E(e)}null==c&&(c=0,i.replaceState(u(u({},i.state),{},{idx:c}),""));let p={get action(){return s},get location(){return e(a,i)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(v,h),l=e,()=>{a.removeEventListener(v,h),l=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s="PUSH";let r=x(p.location,e,t);n&&n(r,e),c=d()+1;let u=k(r,c),h=p.createHref(r);try{i.pushState(u,"",h)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;a.location.assign(h)}o&&l&&l({action:s,location:p.location,delta:1})},replace:function(e,t){s="REPLACE";let r=x(p.location,e,t);n&&n(r,e),c=d();let a=k(r,c),u=p.createHref(r);i.replaceState(a,"",u),o&&l&&l({action:s,location:p.location,delta:0})},go:e=>i.go(e)};return p}function E(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),b(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:S(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function N(e,t){return C(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function C(e,t,n,r){let a=q(("string"===typeof t?_(t):t).pathname||"/",n);if(null==a)return null;let o=T(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let s=0;null==i&&s<o.length;++s){let e=H(a);i=U(o[s],e,r)}return i}function T(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(b(i.relativePath.startsWith(r),'Absolute route path "'.concat(i.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),i.relativePath=i.relativePath.slice(r.length));let s=G([r,i.relativePath]),l=n.concat(i);e.children&&e.children.length>0&&(b(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(s,'".')),T(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:M(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of P(e.path))a(e,t,r);else a(e,t)})),t}function P(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=P(r.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}var O=/^:[\w-]+$/,R=3,L=2,A=1,I=10,z=-2,D=e=>"*"===e;function M(e,t){let n=e.split("/"),r=n.length;return n.some(D)&&(r+=z),t&&(r+=L),n.filter((e=>!D(e))).reduce(((e,t)=>e+(O.test(t)?R:""===t?A:I)),r)}function U(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},o="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,c="/"===o?t:t.slice(o.length)||"/",u=F({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},c),d=e.route;if(!u&&l&&n&&!r[r.length-1].route.index&&(u=F({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:G([o,u.pathname]),pathnameBase:J(G([o,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(o=G([o,u.pathnameBase]))}return i}function F(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=B(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function B(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];w("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function H(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return w(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function q(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function W(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function V(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function $(e){let t=V(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function K(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=_(e):(r=u({},e),b(!r.pathname||!r.pathname.includes("?"),W("?","pathname","search",r)),b(!r.pathname||!r.pathname.includes("#"),W("#","pathname","hash",r)),b(!r.search||!r.search.includes("#"),W("#","search","hash",r)));let o,i=""===e||""===r.pathname,s=i?"/":r.pathname;if(null==s)o=n;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?_(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:Q(r),hash:Y(a)}}(r,o),c=s&&"/"!==s&&s.endsWith("/"),d=(i||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}var G=e=>e.join("/").replace(/\/\/+/g,"/"),J=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Q=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Y=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function X(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var Z=["POST","PUT","PATCH","DELETE"],ee=(new Set(Z),["GET",...Z]);new Set(ee),Symbol("ResetLoaderData");var te=r.createContext(null);te.displayName="DataRouter";var ne=r.createContext(null);ne.displayName="DataRouterState";var re=r.createContext({isTransitioning:!1});re.displayName="ViewTransition";var ae=r.createContext(new Map);ae.displayName="Fetchers";var oe=r.createContext(null);oe.displayName="Await";var ie=r.createContext(null);ie.displayName="Navigation";var se=r.createContext(null);se.displayName="Location";var le=r.createContext({outlet:null,matches:[],isDataRoute:!1});le.displayName="Route";var ce=r.createContext(null);ce.displayName="RouteError";function ue(){return null!=r.useContext(se)}function de(){return b(ue(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(se).location}var he="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function fe(e){r.useContext(ie).static||r.useLayoutEffect(e)}function pe(){let{isDataRoute:e}=r.useContext(le);return e?function(){let{router:e}=Se("useNavigate"),t=je("useNavigate"),n=r.useRef(!1);fe((()=>{n.current=!0}));let a=r.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w(n.current,he),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,u({fromRouteId:t},a)))}),[e,t]);return a}():function(){b(ue(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(te),{basename:t,navigator:n}=r.useContext(ie),{matches:a}=r.useContext(le),{pathname:o}=de(),i=JSON.stringify($(a)),s=r.useRef(!1);fe((()=>{s.current=!0}));let l=r.useCallback((function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(w(s.current,he),!s.current)return;if("number"===typeof r)return void n.go(r);let l=K(r,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:G([t,l.pathname])),(a.replace?n.replace:n.push)(l,a.state,a)}),[t,n,i,o,e]);return l}()}r.createContext(null);function me(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=r.useContext(le),{pathname:a}=de(),o=JSON.stringify($(n));return r.useMemo((()=>K(e,JSON.parse(o),a,"path"===t)),[e,o,a,t])}function ge(e,t,n,a){b(ue(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=r.useContext(ie),{matches:i}=r.useContext(le),s=i[i.length-1],l=s?s.params:{},c=s?s.pathname:"/",d=s?s.pathnameBase:"/",h=s&&s.route;{let e=h&&h.path||"";Ce(c,!h||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(c,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let f,p=de();if(t){var m;let e="string"===typeof t?_(t):t;b("/"===d||(null===(m=e.pathname)||void 0===m?void 0:m.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),f=e}else f=p;let g=f.pathname||"/",v=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");v="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=N(e,{pathname:v});w(h||null!=y,'No routes matched location "'.concat(f.pathname).concat(f.search).concat(f.hash,'" ')),w(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,'Matched leaf route at location "'.concat(f.pathname).concat(f.search).concat(f.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let k=ke(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:G([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:G([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,a);return t&&k?r.createElement(se.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:"POP"}},k):k}function ve(){let e=Ee(),t=X(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=r.createElement(r.Fragment,null,r.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route.")),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:o},n):null,s)}var ye=r.createElement(ve,null),be=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(le.Provider,{value:this.props.routeContext},r.createElement(ce.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function we(e){let{routeContext:t,match:n,children:a}=e,o=r.useContext(te);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(le.Provider,{value:t},a)}function ke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let a=e,o=null===n||void 0===n?void 0:n.errors;if(null!=o){let e=a.findIndex((e=>e.route.id&&void 0!==(null===o||void 0===o?void 0:o[e.route.id])));b(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(o).join(","))),a=a.slice(0,Math.min(a.length,e+1))}let i=!1,s=-1;if(n)for(let r=0;r<a.length;r++){let e=a[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(s=r),e.route.id){let{loaderData:t,errors:r}=n,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||o){i=!0,a=s>=0?a.slice(0,s+1):[a[0]];break}}}return a.reduceRight(((e,l,c)=>{let u,d=!1,h=null,f=null;n&&(u=o&&l.route.id?o[l.route.id]:void 0,h=l.route.errorElement||ye,i&&(s<0&&0===c?(Ce("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,f=null):s===c&&(d=!0,f=l.route.hydrateFallbackElement||null)));let p=t.concat(a.slice(0,c+1)),m=()=>{let t;return t=u?h:d?f:l.route.Component?r.createElement(l.route.Component,null):l.route.element?l.route.element:e,r.createElement(we,{match:l,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===c)?r.createElement(be,{location:n.location,revalidation:n.revalidation,component:h,error:u,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()}),null)}function xe(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Se(e){let t=r.useContext(te);return b(t,xe(e)),t}function _e(e){let t=r.useContext(ne);return b(t,xe(e)),t}function je(e){let t=function(e){let t=r.useContext(le);return b(t,xe(e)),t}(e),n=t.matches[t.matches.length-1];return b(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Ee(){var e;let t=r.useContext(ce),n=_e("useRouteError"),a=je("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[a]}var Ne={};function Ce(e,t,n){t||Ne[e]||(Ne[e]=!0,w(!1,n))}r.memo((function(e){let{routes:t,future:n,state:r}=e;return ge(t,void 0,r,n)}));function Te(e){b(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Pe(e){let{basename:t="/",children:n=null,location:a,navigationType:o="POP",navigator:i,static:s=!1}=e;b(!ue(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=t.replace(/^\/*/,"/"),c=r.useMemo((()=>({basename:l,navigator:i,static:s,future:{}})),[l,i,s]);"string"===typeof a&&(a=_(a));let{pathname:u="/",search:d="",hash:h="",state:f=null,key:p="default"}=a,m=r.useMemo((()=>{let e=q(u,l);return null==e?null:{location:{pathname:e,search:d,hash:h,state:f,key:p},navigationType:o}}),[l,u,d,h,f,p,o]);return w(null!=m,'<Router basename="'.concat(l,'"> is not able to match the URL "').concat(u).concat(d).concat(h,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==m?null:r.createElement(ie.Provider,{value:c},r.createElement(se.Provider,{children:n,value:m}))}function Oe(e){let{children:t,location:n}=e;return ge(Re(t),n)}r.Component;function Re(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return r.Children.forEach(e,((e,a)=>{if(!r.isValidElement(e))return;let o=[...t,a];if(e.type===r.Fragment)return void n.push.apply(n,Re(e.props.children,o));b(e.type===Te,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),b(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Re(e.props.children,o)),n.push(i)})),n}var Le="get",Ae="application/x-www-form-urlencoded";function Ie(e){return null!=e&&"string"===typeof e.tagName}var ze=null;var De=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Me(e){return null==e||De.has(e)?e:(w(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(Ae,'"')),null)}function Ue(e,t){let n,r,a,o,i;if(Ie(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?q(i,t):null,n=e.getAttribute("method")||Le,a=Me(e.getAttribute("enctype"))||Ae,o=new FormData(e)}else if(function(e){return Ie(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ie(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(r=s?q(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Le,a=Me(e.getAttribute("formenctype"))||Me(i.getAttribute("enctype"))||Ae,o=new FormData(i,e),!function(){if(null===ze)try{new FormData(document.createElement("form"),0),ze=!1}catch(e){ze=!0}return ze}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";o.append("".concat(e,"x"),"0"),o.append("".concat(e,"y"),"0")}else t&&o.append(t,r)}}else{if(Ie(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Le,r=null,a=Ae,i=e}var s;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function Fe(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Be(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function He(e){return null!=e&&"string"===typeof e.page}function qe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function We(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,s=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===o?t.filter(((e,t)=>i(e,t)||s(e,t))):"data"===o?t.filter(((t,o)=>{let l=r.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(i(t,o)||s(t,o))return!0;if(t.route.shouldRevalidate){var c;let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function Ve(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a})).flat(1),[...new Set(r)];var r}function $e(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,a)=>{if(t&&!He(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let o=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(o)||(n.add(o),e.push({key:o,link:a})),e}),[])}function Ke(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Ge=new Set([100,101,204,205]);function Je(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===q(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}r.Component;function Qe(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let a,o=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(X(t))return r.createElement(Ye,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),o);if(t instanceof Error)a=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);a=new Error(e)}return r.createElement(Ye,{title:"Application Error!",isOutsideRemixApp:n},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),o)}function Ye(e){var t;let{title:n,renderScripts:a,isOutsideRemixApp:o,children:i}=e,{routeModules:s}=nt();return null!==(t=s.root)&&void 0!==t&&t.Layout&&!o?i:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,n)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,a?r.createElement(ct,null):null)))}function Xe(e,t){return"lazy"===e.mode&&!0===t}function Ze(){let e=r.useContext(te);return Fe(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function et(){let e=r.useContext(ne);return Fe(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var tt=r.createContext(void 0);function nt(){let e=r.useContext(tt);return Fe(e,"You must render this element inside a <HydratedRouter> element"),e}function rt(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function at(e,t,n){if(n&&!lt)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function ot(e){let{page:t}=e,n=o(e,h),{router:a}=Ze(),i=r.useMemo((()=>N(a.routes,t,a.basename)),[a.routes,t,a.basename]);return i?r.createElement(st,u({page:t,matches:i},n)):null}function it(e){let{manifest:t,routeModules:n}=nt(),[a,o]=r.useState([]);return r.useEffect((()=>{let r=!1;return async function(e,t,n){return $e((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await Be(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(qe).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?u(u({},e),{},{rel:"prefetch",as:"style"}):u(u({},e),{},{rel:"prefetch"}))))}(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),a}function st(e){let{page:t,matches:n}=e,a=o(e,f),i=de(),{manifest:s,routeModules:l}=nt(),{basename:c}=Ze(),{loaderData:d,matches:h}=et(),p=r.useMemo((()=>We(t,n,h,s,i,"data")),[t,n,h,s,i]),m=r.useMemo((()=>We(t,n,h,s,i,"assets")),[t,n,h,s,i]),g=r.useMemo((()=>{if(t===i.pathname+i.search+i.hash)return[];let e=new Set,r=!1;if(n.forEach((t=>{var n;let a=s.routes[t.route.id];a&&a.hasLoader&&(!p.some((e=>e.route.id===t.route.id))&&t.route.id in d&&null!==(n=l[t.route.id])&&void 0!==n&&n.shouldRevalidate||a.hasClientLoader?r=!0:e.add(t.route.id))})),0===e.size)return[];let a=Je(t,c);return r&&e.size>0&&a.searchParams.set("_routes",n.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[a.pathname+a.search]}),[c,d,i,s,p,n,t,l]),v=r.useMemo((()=>Ve(m,s)),[m,s]),y=it(m);return r.createElement(r.Fragment,null,g.map((e=>r.createElement("link",u({key:e,rel:"prefetch",as:"fetch",href:e},a)))),v.map((e=>r.createElement("link",u({key:e,rel:"modulepreload",href:e},a)))),y.map((e=>{let{key:t,link:n}=e;return r.createElement("link",u({key:t},n))})))}tt.displayName="FrameworkContext";var lt=!1;function ct(e){let{manifest:t,serverHandoffString:n,isSpaMode:a,renderMeta:i,routeDiscovery:s,ssr:l}=nt(),{router:c,static:h,staticContext:f}=Ze(),{matches:p}=et(),m=Xe(s,l);i&&(i.didRenderScripts=!0);let g=at(p,null,a);r.useEffect((()=>{lt=!0}),[]);let v=r.useMemo((()=>{var a;let i=f?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",s=h?"".concat(null!==(a=t.hmr)&&void 0!==a&&a.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(m?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(g.map(((e,n)=>{let r="route".concat(n),a=t.routes[e.route.id];Fe(a,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:o,clientLoaderModule:i,clientMiddlewareModule:s,hydrateFallbackModule:l,module:c}=a,u=[...o?[{module:o,varName:"".concat(r,"_clientAction")}]:[],...i?[{module:i,varName:"".concat(r,"_clientLoader")}]:[],...s?[{module:s,varName:"".concat(r,"_clientMiddleware")}]:[],...l?[{module:l,varName:"".concat(r,"_HydrateFallback")}]:[],{module:c,varName:"".concat(r,"_main")}];return 1===u.length?"import * as ".concat(r," from ").concat(JSON.stringify(c),";"):[u.map((e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";'))).join("\n"),"const ".concat(r," = {").concat(u.map((e=>"...".concat(e.varName))).join(","),"};")].join("\n")})).join("\n"),"\n  ").concat(m?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=o(e,d),a=new Set(t.state.matches.map((e=>e.route.id))),i=t.state.location.pathname.split("/").filter(Boolean),s=["/"];for(i.pop();i.length>0;)s.push("/".concat(i.join("/"))),i.pop();s.forEach((e=>{let n=N(t.routes,e,t.basename);n&&n.forEach((e=>a.add(e.route.id)))}));let l=[...a].reduce(((e,t)=>Object.assign(e,{[t]:r.routes[t]})),{});return u(u({},r),{},{routes:l,sri:!!n||void 0})}(t,c),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map(((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t))).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return r.createElement(r.Fragment,null,r.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(i),type:void 0})),r.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(s),type:"module",async:!0})))}),[]),y=lt?[]:(b=t.entry.imports.concat(Ve(g,t,{includeHydrateFallback:!0})),[...new Set(b)]);var b;let w="object"===typeof t.sri?t.sri:{};return lt?null:r.createElement(r.Fragment,null,"object"===typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:w})}}):null,m?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:w[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:w[t.entry.module],suppressHydrationWarning:!0}),y.map((t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:w[t],suppressHydrationWarning:!0}))),v)}function ut(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var dt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{dt&&(window.__reactRouterVersion="7.6.2")}catch(Da){}function ht(e){let{basename:t,children:n,window:a}=e,o=r.useRef();null==o.current&&(o.current=y({window:a,v5Compat:!0}));let i=o.current,[s,l]=r.useState({action:i.action,location:i.location}),c=r.useCallback((e=>{r.startTransition((()=>l(e)))}),[l]);return r.useLayoutEffect((()=>i.listen(c)),[i,c]),r.createElement(Pe,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i})}var ft=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,pt=r.forwardRef((function(e,t){let n,{onClick:a,discover:i="render",prefetch:s="none",relative:l,reloadDocument:c,replace:d,state:h,target:f,to:m,preventScrollReset:g,viewTransition:v}=e,y=o(e,p),{basename:k}=r.useContext(ie),x="string"===typeof m&&ft.test(m),_=!1;if("string"===typeof m&&x&&(n=m,dt))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=q(t.pathname,k);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:_=!0}catch(Da){w(!1,'<Link to="'.concat(m,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let j=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};b(ue(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:a}=r.useContext(ie),{hash:o,pathname:i,search:s}=me(e,{relative:t}),l=i;return"/"!==n&&(l="/"===i?n:G([n,i])),a.createHref({pathname:l,search:s,hash:o})}(m,{relative:l}),[E,N,C]=function(e,t){let n=r.useContext(tt),[a,o]=r.useState(!1),[i,s]=r.useState(!1),{onFocus:l,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:h}=t,f=r.useRef(null);r.useEffect((()=>{if("render"===e&&s(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{s(e.isIntersecting)}))}),{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}}),[e]),r.useEffect((()=>{if(a){let e=setTimeout((()=>{s(!0)}),100);return()=>{clearTimeout(e)}}}),[a]);let p=()=>{o(!0)},m=()=>{o(!1),s(!1)};return n?"intent"!==e?[i,f,{}]:[i,f,{onFocus:rt(l,p),onBlur:rt(c,m),onMouseEnter:rt(u,p),onMouseLeave:rt(d,m),onTouchStart:rt(h,p)}]:[!1,f,{}]}(s,y),T=function(e){let{target:t,replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=pe(),c=de(),u=me(e,{relative:i});return r.useCallback((r=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(r,t)){r.preventDefault();let t=void 0!==n?n:S(c)===S(u);l(e,{replace:t,state:a,preventScrollReset:o,relative:i,viewTransition:s})}}),[c,l,u,n,a,t,e,o,i,s])}(m,{replace:d,state:h,target:f,preventScrollReset:g,relative:l,viewTransition:v});let P=r.createElement("a",u(u(u({},y),C),{},{href:n||j,onClick:_||c?a:function(e){a&&a(e),e.defaultPrevented||T(e)},ref:ut(t,N),target:f,"data-discover":x||"render"!==i?void 0:"true"}));return E&&!x?r.createElement(r.Fragment,null,P,r.createElement(ot,{page:j})):P}));pt.displayName="Link",r.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:s=!1,style:l,to:c,viewTransition:d,children:h}=e,f=o(e,m),p=me(c,{relative:f.relative}),g=de(),v=r.useContext(ne),{navigator:y,basename:w}=r.useContext(ie),k=null!=v&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.useContext(re);b(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=vt("useViewTransitionState"),o=me(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=q(n.currentLocation.pathname,a)||n.currentLocation.pathname,s=q(n.nextLocation.pathname,a)||n.nextLocation.pathname;return null!=F(o.pathname,s)||null!=F(o.pathname,i)}(p)&&!0===d,x=y.encodeLocation?y.encodeLocation(p).pathname:p.pathname,S=g.pathname,_=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;a||(S=S.toLowerCase(),_=_?_.toLowerCase():null,x=x.toLowerCase()),_&&w&&(_=q(_,w)||_);const j="/"!==x&&x.endsWith("/")?x.length-1:x.length;let E,N=S===x||!s&&S.startsWith(x)&&"/"===S.charAt(j),C=null!=_&&(_===x||!s&&_.startsWith(x)&&"/"===_.charAt(x.length)),T={isActive:N,isPending:C,isTransitioning:k},P=N?n:void 0;E="function"===typeof i?i(T):[i,N?"active":null,C?"pending":null,k?"transitioning":null].filter(Boolean).join(" ");let O="function"===typeof l?l(T):l;return r.createElement(pt,u(u({},f),{},{"aria-current":P,className:E,ref:t,style:O,to:c,viewTransition:d}),"function"===typeof h?h(T):h)})).displayName="NavLink";var mt=r.forwardRef(((e,t)=>{let{discover:n="render",fetcherKey:a,navigate:i,reloadDocument:s,replace:l,state:c,method:d=Le,action:h,onSubmit:f,relative:p,preventScrollReset:m,viewTransition:v}=e,y=o(e,g),w=wt(),k=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=r.useContext(ie),a=r.useContext(le);b(a,"useFormAction must be used inside a RouteContext");let[o]=a.matches.slice(-1),i=u({},me(e||".",{relative:t})),s=de();if(null==e){i.search=s.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?"?".concat(n):""}}e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:G([n,i.pathname]));return S(i)}(h,{relative:p}),x="get"===d.toLowerCase()?"get":"post",_="string"===typeof h&&ft.test(h);return r.createElement("form",u(u({ref:t,method:x,action:k,onSubmit:s?f:e=>{if(f&&f(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||d;w(t||e.currentTarget,{fetcherKey:a,method:n,navigate:i,replace:l,state:c,relative:p,preventScrollReset:m,viewTransition:v})}},y),{},{"data-discover":_||"render"!==n?void 0:"true"}))}));function gt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function vt(e){let t=r.useContext(te);return b(t,gt(e)),t}mt.displayName="Form";var yt=0,bt=()=>"__".concat(String(++yt),"__");function wt(){let{router:e}=vt("useSubmit"),{basename:t}=r.useContext(ie),n=je("useRouteId");return r.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:o,method:i,encType:s,formData:l,body:c}=Ue(r,t);if(!1===a.navigate){let t=a.fetcherKey||bt();await e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||i,formEncType:a.encType||s,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||i,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[e,t,n])}class kt extends Error{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"FunctionsError",n=arguments.length>2?arguments[2]:void 0;super(e),this.name=t,this.context=n}}class xt extends kt{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class St extends kt{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class _t extends kt{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var jt;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(jt||(jt={}));var Et=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class Nt{constructor(e){let{headers:t={},customFetch:r,region:a=jt.Any}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=t,this.region=a,this.fetch=(e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,630)).then((e=>{let{default:n}=e;return n(...t)}))}:fetch),function(){return t(...arguments)}})(r)}setAuth(e){this.headers.Authorization="Bearer ".concat(e)}invoke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n;return Et(this,void 0,void 0,(function*(){try{const{headers:r,method:a,body:o}=t;let i,s={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(s["x-region"]=l),o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!==typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",i=o):"string"===typeof o?(s["Content-Type"]="text/plain",i=o):"undefined"!==typeof FormData&&o instanceof FormData?i=o:(s["Content-Type"]="application/json",i=JSON.stringify(o)));const c=yield this.fetch("".concat(this.url,"/").concat(e),{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),r),body:i}).catch((e=>{throw new xt(e)})),u=c.headers.get("x-relay-error");if(u&&"true"===u)throw new St(c);if(!c.ok)throw new _t(c);let d,h=(null!==(n=c.headers.get("Content-Type"))&&void 0!==n?n:"text/plain").split(";")[0].trim();return d="application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),{data:d,error:null}}catch(r){return{data:null,error:r}}}))}}var Ct=n(980);const{PostgrestClient:Tt,PostgrestQueryBuilder:Pt,PostgrestFilterBuilder:Ot,PostgrestTransformBuilder:Rt,PostgrestBuilder:Lt,PostgrestError:At}=Ct;let It;It="undefined"===typeof window?n(952):window.WebSocket;const zt=It,Dt={"X-Client-Info":"realtime-js/".concat("2.11.10")};var Mt,Ut,Ft,Bt,Ht,qt;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(Mt||(Mt={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(Ut||(Ut={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(Ft||(Ft={})),function(e){e.websocket="websocket"}(Bt||(Bt={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(Ht||(Ht={}));class Wt{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"===typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),n=new TextDecoder;return this._decodeBroadcast(e,t,n)}_decodeBroadcast(e,t,n){const r=t.getUint8(1),a=t.getUint8(2);let o=this.HEADER_LENGTH+2;const i=n.decode(e.slice(o,o+r));o+=r;const s=n.decode(e.slice(o,o+a));o+=a;return{ref:null,topic:i,event:s,payload:JSON.parse(n.decode(e.slice(o,e.byteLength)))}}}class Vt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(qt||(qt={}));const $t=function(e,t){var n;const r=null!==(n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipTypes)&&void 0!==n?n:[];return Object.keys(t).reduce(((n,a)=>(n[a]=Kt(a,e,t,r),n)),{})},Kt=(e,t,n,r)=>{const a=t.find((t=>t.name===e)),o=null===a||void 0===a?void 0:a.type,i=n[e];return o&&!r.includes(o)?Gt(o,i):Jt(i)},Gt=(e,t)=>{if("_"===e.charAt(0)){const n=e.slice(1,e.length);return Zt(t,n)}switch(e){case qt.bool:return Qt(t);case qt.float4:case qt.float8:case qt.int2:case qt.int4:case qt.int8:case qt.numeric:case qt.oid:return Yt(t);case qt.json:case qt.jsonb:return Xt(t);case qt.timestamp:return en(t);case qt.abstime:case qt.date:case qt.daterange:case qt.int4range:case qt.int8range:case qt.money:case qt.reltime:case qt.text:case qt.time:case qt.timestamptz:case qt.timetz:case qt.tsrange:case qt.tstzrange:default:return Jt(t)}},Jt=e=>e,Qt=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Yt=e=>{if("string"===typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Xt=e=>{if("string"===typeof e)try{return JSON.parse(e)}catch(t){return console.log("JSON parse error: ".concat(t)),e}return e},Zt=(e,t)=>{if("string"!==typeof e)return e;const n=e.length-1,r=e[n];if("{"===e[0]&&"}"===r){let r;const o=e.slice(1,n);try{r=JSON.parse("["+o+"]")}catch(a){r=o?o.split(","):[]}return r.map((e=>Gt(t,e)))}return e},en=e=>"string"===typeof e?e.replace(" ","T"):e,tn=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class nn{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e4;this.channel=e,this.event=t,this.payload=n,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var n;return this._hasReceived(e)&&t(null===(n=this.receivedResp)||void 0===n?void 0:n.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},(e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive(e){let{status:t,response:n}=e;this.recHooks.filter((e=>e.status===t)).forEach((e=>e.callback(n)))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var rn,an,on,sn;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(rn||(rn={}));class ln{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(null===t||void 0===t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},(e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=ln.syncState(this.state,e,t,n),this.pendingDiffs.forEach((e=>{this.state=ln.syncDiff(this.state,e,t,n)})),this.pendingDiffs=[],r()})),this.channel._on(n.diff,{},(e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=ln.syncDiff(this.state,e,t,n),r())})),this.onJoin(((e,t,n)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:n})})),this.onLeave(((e,t,n)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:n})})),this.onSync((()=>{this.channel._trigger("presence",{event:"sync"})}))}static syncState(e,t,n,r){const a=this.cloneDeep(e),o=this.transformState(t),i={},s={};return this.map(a,((e,t)=>{o[e]||(s[e]=t)})),this.map(o,((e,t)=>{const n=a[e];if(n){const r=t.map((e=>e.presence_ref)),a=n.map((e=>e.presence_ref)),o=t.filter((e=>a.indexOf(e.presence_ref)<0)),l=n.filter((e=>r.indexOf(e.presence_ref)<0));o.length>0&&(i[e]=o),l.length>0&&(s[e]=l)}else i[e]=t})),this.syncDiff(a,{joins:i,leaves:s},n,r)}static syncDiff(e,t,n,r){const{joins:a,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return n||(n=()=>{}),r||(r=()=>{}),this.map(a,((t,r)=>{var a;const o=null!==(a=e[t])&&void 0!==a?a:[];if(e[t]=this.cloneDeep(r),o.length>0){const n=e[t].map((e=>e.presence_ref)),r=o.filter((e=>n.indexOf(e.presence_ref)<0));e[t].unshift(...r)}n(t,o,r)})),this.map(o,((t,n)=>{let a=e[t];if(!a)return;const o=n.map((e=>e.presence_ref));a=a.filter((e=>o.indexOf(e.presence_ref)<0)),e[t]=a,r(t,a,n),0===a.length&&delete e[t]})),e}static map(e,t){return Object.getOwnPropertyNames(e).map((n=>t(n,e[n])))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce(((t,n)=>{const r=e[n];return t[n]="metas"in r?r.metas.map((e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e))):r,t}),{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(an||(an={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(on||(on={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(sn||(sn={}));class cn{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}},n=arguments.length>2?arguments[2]:void 0;this.topic=e,this.params=t,this.socket=n,this.bindings={},this.state=Ut.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new nn(this,Ft.join,this.params,this.timeout),this.rejoinTimer=new Vt((()=>this._rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=Ut.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((e=>e.send())),this.pushBuffer=[]})),this._onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel","close ".concat(this.topic," ").concat(this._joinRef())),this.state=Ut.closed,this.socket._remove(this)})),this._onError((e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel","error ".concat(this.topic),e),this.state=Ut.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this._isJoining()&&(this.socket.log("channel","timeout ".concat(this.topic),this.joinPush.timeout),this.state=Ut.errored,this.rejoinTimer.scheduleTimeout())})),this._on(Ft.reply,{},((e,t)=>{this._trigger(this._replyEventName(t),e)})),this.presence=new ln(this),this.broadcastEndpointURL=tn(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.timeout;var n,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:a,presence:o,private:i}}=this.params;this._onError((t=>null===e||void 0===e?void 0:e(sn.CHANNEL_ERROR,t))),this._onClose((()=>null===e||void 0===e?void 0:e(sn.CLOSED)));const s={},l={broadcast:a,presence:o,postgres_changes:null!==(r=null===(n=this.bindings.postgres_changes)||void 0===n?void 0:n.map((e=>e.filter)))&&void 0!==r?r:[],private:i};this.socket.accessTokenValue&&(s.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},s)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",(async t=>{let{postgres_changes:n}=t;var r;if(this.socket.setAuth(),void 0!==n){const t=this.bindings.postgres_changes,a=null!==(r=null===t||void 0===t?void 0:t.length)&&void 0!==r?r:0,o=[];for(let r=0;r<a;r++){const a=t[r],{filter:{event:i,schema:s,table:l,filter:c}}=a,u=n&&n[r];if(!u||u.event!==i||u.schema!==s||u.table!==l||u.filter!==c)return this.unsubscribe(),this.state=Ut.errored,void(null===e||void 0===e||e(sn.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},a),{id:u.id}))}return this.bindings.postgres_changes=o,void(e&&e(sn.SUBSCRIBED))}null===e||void 0===e||e(sn.SUBSCRIBED)})).receive("error",(t=>{this.state=Ut.errored,null===e||void 0===e||e(sn.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))})).receive("timeout",(()=>{null===e||void 0===e||e(sn.TIMED_OUT)}))}return this}presenceState(){return this.presence.state}async track(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return await this.send({type:"presence",event:"untrack"},e)}on(e,t,n){return this._on(e,t,n)}async send(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n,r;if(this._canPush()||"broadcast"!==e.type)return new Promise((n=>{var r,a,o;const i=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(a=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===a?void 0:a.broadcast)||void 0===o?void 0:o.ack)||n("ok"),i.receive("ok",(()=>n("ok"))),i.receive("error",(()=>n("error"))),i.receive("timeout",(()=>n("timed out")))}));{const{event:o,payload:i}=e,s={method:"POST",headers:{Authorization:this.socket.accessTokenValue?"Bearer ".concat(this.socket.accessTokenValue):"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:i,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,s,null!==(n=t.timeout)&&void 0!==n?n:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(a){return"AbortError"===a.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this.state=Ut.leaving;const t=()=>{this.socket.log("channel","leave ".concat(this.topic)),this._trigger(Ft.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise((n=>{const r=new nn(this,Ft.leave,{},e);r.receive("ok",(()=>{t(),n("ok")})).receive("timeout",(()=>{t(),n("timed out")})).receive("error",(()=>{n("error")})),r.send(),this._canPush()||r.trigger("ok",{})}))}teardown(){this.pushBuffer.forEach((e=>e.destroy())),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,n){const r=new AbortController,a=setTimeout((()=>r.abort()),n),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(a),o}_push(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.timeout;if(!this.joinedOnce)throw"tried to push '".concat(e,"' to '").concat(this.topic,"' before joining. Use channel.subscribe() before pushing events");let r=new nn(this,e,t,n);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,n){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,n){var r,a;const o=e.toLocaleLowerCase(),{close:i,error:s,leave:l,join:c}=Ft;if(n&&[i,s,l,c].indexOf(o)>=0&&n!==this._joinRef())return;let u=this._onMessage(o,t,n);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter((e=>{var t,n,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(n=e.filter)||void 0===n?void 0:n.event)||void 0===r?void 0:r.toLocaleLowerCase())===o})).map((e=>e.callback(u,n))):null===(a=this.bindings[o])||void 0===a||a.filter((e=>{var n,r,a,i,s,l;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,i=null===(n=e.filter)||void 0===n?void 0:n.event;return o&&(null===(r=t.ids)||void 0===r?void 0:r.includes(o))&&("*"===i||(null===i||void 0===i?void 0:i.toLocaleLowerCase())===(null===(a=t.data)||void 0===a?void 0:a.type.toLocaleLowerCase()))}{const n=null===(s=null===(i=null===e||void 0===e?void 0:e.filter)||void 0===i?void 0:i.event)||void 0===s?void 0:s.toLocaleLowerCase();return"*"===n||n===(null===(l=null===t||void 0===t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o})).map((e=>{if("object"===typeof u&&"ids"in u){const e=u.data,{schema:t,table:n,commit_timestamp:r,type:a,errors:o}=e,i={schema:t,table:n,commit_timestamp:r,eventType:a,new:{},old:{},errors:o};u=Object.assign(Object.assign({},i),this._getPayloadRecords(e))}e.callback(u,n)}))}_isClosed(){return this.state===Ut.closed}_isJoined(){return this.state===Ut.joined}_isJoining(){return this.state===Ut.joining}_isLeaving(){return this.state===Ut.leaving}_replyEventName(e){return"chan_reply_".concat(e)}_on(e,t,n){const r=e.toLocaleLowerCase(),a={type:r,filter:t,callback:n};return this.bindings[r]?this.bindings[r].push(a):this.bindings[r]=[a],this}_off(e,t){const n=e.toLocaleLowerCase();return this.bindings[n]=this.bindings[n].filter((e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===n&&cn.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Ft.close,{},e)}_onError(e){this._on(Ft.error,{},(t=>e(t)))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Ut.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=$t(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=$t(e.columns,e.old_record)),t}}const un=()=>{};class dn{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=Dt,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=un,this.ref=0,this.logger=un,this.conn=null,this.sendBuffer=[],this.serializer=new Wt,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,630)).then((e=>{let{default:n}=e;return n(...t)}))}:fetch),function(){return t(...arguments)}},this.endPoint="".concat(e,"/").concat(Bt.websocket),this.httpEndpoint=tn(e),(null===t||void 0===t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null===t||void 0===t?void 0:t.params)&&(this.params=t.params),(null===t||void 0===t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null===t||void 0===t?void 0:t.timeout)&&(this.timeout=t.timeout),(null===t||void 0===t?void 0:t.logger)&&(this.logger=t.logger),((null===t||void 0===t?void 0:t.logLevel)||(null===t||void 0===t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null===t||void 0===t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const a=null===(r=null===t||void 0===t?void 0:t.params)||void 0===r?void 0:r.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=(null===t||void 0===t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null===t||void 0===t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null===t||void 0===t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Vt((async()=>{this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null===t||void 0===t?void 0:t.fetch),null===t||void 0===t?void 0:t.worker){if("undefined"!==typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null===t||void 0===t?void 0:t.worker)||!1,this.workerUrl=null===t||void 0===t?void 0:t.workerUrl}this.accessToken=(null===t||void 0===t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=zt),this.transport){const e="undefined"!==typeof window&&this.transport===window.WebSocket;return this.conn=e?new this.transport(this.endpointURL()):new this.transport(this.endpointURL(),void 0,{headers:this.headers}),void this.setupConnection()}this.conn=new hn(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!==t&&void 0!==t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach((e=>e.teardown())))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return this.channels=this.channels.filter((t=>t._joinRef!==e._joinRef)),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map((e=>e.unsubscribe())));return this.channels=[],this.disconnect(),e}log(e,t,n){this.logger(e,t,n)}connectionState(){switch(this.conn&&this.conn.readyState){case Mt.connecting:return Ht.Connecting;case Mt.open:return Ht.Open;case Mt.closing:return Ht.Closing;default:return Ht.Closed}}isConnected(){return this.connectionState()===Ht.Open}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};const n="realtime:".concat(e),r=this.getChannels().find((e=>e.topic===n));if(r)return r;{const n=new cn("realtime:".concat(e),t,this);return this.channels.push(n),n}}push(e){const{topic:t,event:n,payload:r,ref:a}=e,o=()=>{this.encode(e,(e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)}))};this.log("push","".concat(t," ").concat(n," (").concat(a,")"),r),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(){let e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=e&&(this.accessTokenValue=e,this.channels.forEach((t=>{e&&t.updateJoinPayload({access_token:e,version:this.headers&&this.headers["X-Client-Info"]}),t.joinedOnce&&t._isJoined()&&t._push(Ft.access_token,{access_token:e})})))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((e=>e())),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find((t=>t.topic===e&&(t._isJoined()||t._isJoining())));t&&(this.log("transport",'leaving duplicate topic "'.concat(e,'"')),t.unsubscribe())}_remove(e){this.channels=this.channels.filter((t=>t.topic!==e.topic))}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,(e=>{let{topic:t,event:n,payload:r,ref:a}=e;"phoenix"===t&&"phx_reply"===n&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive","".concat(r.status||""," ").concat(t," ").concat(n," ").concat(a&&"("+a+")"||""),r),Array.from(this.channels).filter((e=>e._isMember(t))).forEach((e=>e._trigger(n,r,a))),this.stateChangeCallbacks.message.forEach((t=>t(e)))}))}_onConnOpen(){if(this.log("transport","connected to ".concat(this.endpointURL())),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker","starting worker for from ".concat(this.workerUrl)):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this.sendHeartbeat()),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach((e=>e()))}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((t=>t(e)))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((t=>t(e)))}_triggerChanError(){this.channels.forEach((e=>e._trigger(Ft.error)))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const n=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return"".concat(e).concat(n).concat(r)}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class hn{constructor(e,t,n){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Mt.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=n.close}}class fn extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function pn(e){return"object"===typeof e&&null!==e&&"__isStorageError"in e}class mn extends fn{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class gn extends fn{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var vn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const yn=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,630)).then((e=>{let{default:n}=e;return n(...t)}))}:fetch),function(){return t(...arguments)}},bn=e=>{if(Array.isArray(e))return e.map((e=>bn(e)));if("function"===typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach((e=>{let[n,r]=e;const a=n.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace(/[-_]/g,"")));t[a]=bn(r)})),t};var wn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const kn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),xn=(e,t,r)=>wn(void 0,void 0,void 0,(function*(){const a=yield vn(void 0,void 0,void 0,(function*(){return"undefined"===typeof Response?(yield Promise.resolve().then(n.bind(n,630))).Response:Response}));e instanceof a&&!(null===r||void 0===r?void 0:r.noResolveJson)?e.json().then((n=>{t(new mn(kn(n),e.status||500))})).catch((e=>{t(new gn(kn(e),e))})):t(new gn(kn(e),e))}));function Sn(e,t,n,r,a,o){return wn(this,void 0,void 0,(function*(){return new Promise(((i,s)=>{e(n,((e,t,n,r)=>{const a={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json"},null===t||void 0===t?void 0:t.headers),r&&(a.body=JSON.stringify(r)),Object.assign(Object.assign({},a),n))})(t,r,a,o)).then((e=>{if(!e.ok)throw e;return(null===r||void 0===r?void 0:r.noResolveJson)?e:e.json()})).then((e=>i(e))).catch((e=>xn(e,s,r)))}))}))}function _n(e,t,n,r){return wn(this,void 0,void 0,(function*(){return Sn(e,"GET",t,n,r)}))}function jn(e,t,n,r,a){return wn(this,void 0,void 0,(function*(){return Sn(e,"POST",t,r,a,n)}))}function En(e,t,n,r,a){return wn(this,void 0,void 0,(function*(){return Sn(e,"DELETE",t,r,a,n)}))}var Nn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Cn={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Tn={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Pn{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;this.url=e,this.headers=t,this.bucketId=n,this.fetch=yn(r)}uploadOrUpdate(e,t,n,r){return Nn(this,void 0,void 0,(function*(){try{let a;const o=Object.assign(Object.assign({},Tn),r);let i=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const s=o.metadata;"undefined"!==typeof Blob&&n instanceof Blob?(a=new FormData,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s)),a.append("",n)):"undefined"!==typeof FormData&&n instanceof FormData?(a=n,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s))):(a=n,i["cache-control"]="max-age=".concat(o.cacheControl),i["content-type"]=o.contentType,s&&(i["x-metadata"]=this.toBase64(this.encodeMetadata(s)))),(null===r||void 0===r?void 0:r.headers)&&(i=Object.assign(Object.assign({},i),r.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch("".concat(this.url,"/object/").concat(c),Object.assign({method:e,body:a,headers:i},(null===o||void 0===o?void 0:o.duplex)?{duplex:o.duplex}:{})),d=yield u.json();if(u.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(a){if(pn(a))return{data:null,error:a};throw a}}))}upload(e,t,n){return Nn(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,n)}))}uploadToSignedUrl(e,t,n,r){return Nn(this,void 0,void 0,(function*(){const a=this._removeEmptyFolders(e),o=this._getFinalPath(a),i=new URL(this.url+"/object/upload/sign/".concat(o));i.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:Tn.upsert},r),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!==typeof Blob&&n instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",n)):"undefined"!==typeof FormData&&n instanceof FormData?(e=n,e.append("cacheControl",t.cacheControl)):(e=n,o["cache-control"]="max-age=".concat(t.cacheControl),o["content-type"]=t.contentType);const s=yield this.fetch(i.toString(),{method:"PUT",body:e,headers:o}),l=yield s.json();if(s.ok)return{data:{path:a,fullPath:l.Key},error:null};return{data:null,error:l}}catch(s){if(pn(s))return{data:null,error:s};throw s}}))}createSignedUploadUrl(e,t){return Nn(this,void 0,void 0,(function*(){try{let n=this._getFinalPath(e);const r=Object.assign({},this.headers);(null===t||void 0===t?void 0:t.upsert)&&(r["x-upsert"]="true");const a=yield jn(this.fetch,"".concat(this.url,"/object/upload/sign/").concat(n),{},{headers:r}),o=new URL(this.url+a.url),i=o.searchParams.get("token");if(!i)throw new fn("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:i},error:null}}catch(n){if(pn(n))return{data:null,error:n};throw n}}))}update(e,t,n){return Nn(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,n)}))}move(e,t,n){return Nn(this,void 0,void 0,(function*(){try{return{data:yield jn(this.fetch,"".concat(this.url,"/object/move"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===n||void 0===n?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(pn(r))return{data:null,error:r};throw r}}))}copy(e,t,n){return Nn(this,void 0,void 0,(function*(){try{return{data:{path:(yield jn(this.fetch,"".concat(this.url,"/object/copy"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===n||void 0===n?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(pn(r))return{data:null,error:r};throw r}}))}createSignedUrl(e,t,n){return Nn(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(e),a=yield jn(this.fetch,"".concat(this.url,"/object/sign/").concat(r),Object.assign({expiresIn:t},(null===n||void 0===n?void 0:n.transform)?{transform:n.transform}:{}),{headers:this.headers});const o=(null===n||void 0===n?void 0:n.download)?"&download=".concat(!0===n.download?"":n.download):"";return a={signedUrl:encodeURI("".concat(this.url).concat(a.signedURL).concat(o))},{data:a,error:null}}catch(r){if(pn(r))return{data:null,error:r};throw r}}))}createSignedUrls(e,t,n){return Nn(this,void 0,void 0,(function*(){try{const r=yield jn(this.fetch,"".concat(this.url,"/object/sign/").concat(this.bucketId),{expiresIn:t,paths:e},{headers:this.headers}),a=(null===n||void 0===n?void 0:n.download)?"&download=".concat(!0===n.download?"":n.download):"";return{data:r.map((e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI("".concat(this.url).concat(e.signedURL).concat(a)):null}))),error:null}}catch(r){if(pn(r))return{data:null,error:r};throw r}}))}download(e,t){return Nn(this,void 0,void 0,(function*(){const n="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{}),a=r?"?".concat(r):"";try{const t=this._getFinalPath(e),r=yield _n(this.fetch,"".concat(this.url,"/").concat(n,"/").concat(t).concat(a),{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(o){if(pn(o))return{data:null,error:o};throw o}}))}info(e){return Nn(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{const e=yield _n(this.fetch,"".concat(this.url,"/object/info/").concat(t),{headers:this.headers});return{data:bn(e),error:null}}catch(n){if(pn(n))return{data:null,error:n};throw n}}))}exists(e){return Nn(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{return yield function(e,t,n,r){return wn(this,void 0,void 0,(function*(){return Sn(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)}))}(this.fetch,"".concat(this.url,"/object/").concat(t),{headers:this.headers}),{data:!0,error:null}}catch(n){if(pn(n)&&n instanceof gn){const e=n.originalError;if([400,404].includes(null===e||void 0===e?void 0:e.status))return{data:!1,error:n}}throw n}}))}getPublicUrl(e,t){const n=this._getFinalPath(e),r=[],a=(null===t||void 0===t?void 0:t.download)?"download=".concat(!0===t.download?"":t.download):"";""!==a&&r.push(a);const o="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image":"object",i=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{});""!==i&&r.push(i);let s=r.join("&");return""!==s&&(s="?".concat(s)),{data:{publicUrl:encodeURI("".concat(this.url,"/").concat(o,"/public/").concat(n).concat(s))}}}remove(e){return Nn(this,void 0,void 0,(function*(){try{return{data:yield En(this.fetch,"".concat(this.url,"/object/").concat(this.bucketId),{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(pn(t))return{data:null,error:t};throw t}}))}list(e,t,n){return Nn(this,void 0,void 0,(function*(){try{const r=Object.assign(Object.assign(Object.assign({},Cn),t),{prefix:e||""});return{data:yield jn(this.fetch,"".concat(this.url,"/object/list/").concat(this.bucketId),r,{headers:this.headers},n),error:null}}catch(r){if(pn(r))return{data:null,error:r};throw r}}))}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!==typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return"".concat(this.bucketId,"/").concat(e)}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push("width=".concat(e.width)),e.height&&t.push("height=".concat(e.height)),e.resize&&t.push("resize=".concat(e.resize)),e.format&&t.push("format=".concat(e.format)),e.quality&&t.push("quality=".concat(e.quality)),t.join("&")}}const On={"X-Client-Info":"storage-js/".concat("2.7.1")};var Rn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class Ln{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;this.url=e,this.headers=Object.assign(Object.assign({},On),t),this.fetch=yn(n)}listBuckets(){return Rn(this,void 0,void 0,(function*(){try{return{data:yield _n(this.fetch,"".concat(this.url,"/bucket"),{headers:this.headers}),error:null}}catch(e){if(pn(e))return{data:null,error:e};throw e}}))}getBucket(e){return Rn(this,void 0,void 0,(function*(){try{return{data:yield _n(this.fetch,"".concat(this.url,"/bucket/").concat(e),{headers:this.headers}),error:null}}catch(t){if(pn(t))return{data:null,error:t};throw t}}))}createBucket(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{public:!1};return Rn(this,void 0,void 0,(function*(){try{return{data:yield jn(this.fetch,"".concat(this.url,"/bucket"),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(pn(n))return{data:null,error:n};throw n}}))}updateBucket(e,t){return Rn(this,void 0,void 0,(function*(){try{const n=yield function(e,t,n,r,a){return wn(this,void 0,void 0,(function*(){return Sn(e,"PUT",t,r,a,n)}))}(this.fetch,"".concat(this.url,"/bucket/").concat(e),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:n,error:null}}catch(n){if(pn(n))return{data:null,error:n};throw n}}))}emptyBucket(e){return Rn(this,void 0,void 0,(function*(){try{return{data:yield jn(this.fetch,"".concat(this.url,"/bucket/").concat(e,"/empty"),{},{headers:this.headers}),error:null}}catch(t){if(pn(t))return{data:null,error:t};throw t}}))}deleteBucket(e){return Rn(this,void 0,void 0,(function*(){try{return{data:yield En(this.fetch,"".concat(this.url,"/bucket/").concat(e),{},{headers:this.headers}),error:null}}catch(t){if(pn(t))return{data:null,error:t};throw t}}))}}class An extends Ln{constructor(e){super(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},arguments.length>2?arguments[2]:void 0)}from(e){return new Pn(this.url,this.headers,e,this.fetch)}}let In="";In="undefined"!==typeof Deno?"deno":"undefined"!==typeof document?"web":"undefined"!==typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const zn={headers:{"X-Client-Info":"supabase-js-".concat(In,"/").concat("2.50.0")}},Dn={schema:"public"},Mn={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Un={};var Fn=n(630),Bn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Hn=e=>{let t;return t=e||("undefined"===typeof fetch?Fn.default:fetch),function(){return t(...arguments)}},qn=(e,t,n)=>{const r=Hn(n),a="undefined"===typeof Headers?Fn.Headers:Headers;return(n,o)=>Bn(void 0,void 0,void 0,(function*(){var i;const s=null!==(i=yield t())&&void 0!==i?i:e;let l=new a(null===o||void 0===o?void 0:o.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization","Bearer ".concat(s)),r(n,Object.assign(Object.assign({},o),{headers:l}))}))};var Wn=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Vn="2.70.0",$n=3e4,Kn=9e4,Gn={"X-Client-Info":"gotrue-js/".concat(Vn)},Jn="X-Supabase-Api-Version",Qn={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Yn=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Xn extends Error{constructor(e,t,n){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=n}}function Zn(e){return"object"===typeof e&&null!==e&&"__isAuthError"in e}class er extends Xn{constructor(e,t,n){super(e,t,n),this.name="AuthApiError",this.status=t,this.code=n}}class tr extends Xn{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class nr extends Xn{constructor(e,t,n,r){super(e,n,r),this.name=t,this.status=n}}class rr extends nr{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class ar extends nr{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class or extends nr{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class ir extends nr{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class sr extends nr{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class lr extends nr{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function cr(e){return Zn(e)&&"AuthRetryableFetchError"===e.name}class ur extends nr{constructor(e,t,n){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=n}}class dr extends nr{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const hr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),fr=" \t\n\r=".split(""),pr=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<fr.length;t+=1)e[fr[t].charCodeAt(0)]=-2;for(let t=0;t<hr.length;t+=1)e[hr[t].charCodeAt(0)]=t;return e})();function mr(e,t,n){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(hr[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(hr[e]),t.queuedBits-=6}}function gr(e,t,n){const r=pr[e];if(!(r>-1)){if(-2===r)return;throw new Error('Invalid Base64-URL character "'.concat(String.fromCharCode(e),'"'))}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function vr(e){const t=[],n=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},o=e=>{!function(e,t,n){if(0===t.utf8seq){if(e<=127)return void n(e);for(let n=1;n<6;n+=1)if(0===(e>>7-n&1)){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&n(t.codepoint)}}(e,r,n)};for(let i=0;i<e.length;i+=1)gr(e.charCodeAt(i),a,o);return t.join("")}function yr(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error("Unrecognized Unicode codepoint: ".concat(e.toString(16)))}t(e)}function br(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(n+1)-56320&65535|t),n+=1}yr(r,t)}}function wr(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let a=0;a<e.length;a+=1)gr(e.charCodeAt(a),n,r);return new Uint8Array(t)}function kr(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach((e=>mr(e,n,r))),mr(null,n,r),t.join("")}const xr=()=>"undefined"!==typeof window&&"undefined"!==typeof document,Sr={tested:!1,writable:!1},_r=()=>{if(!xr())return!1;try{if("object"!==typeof globalThis.localStorage)return!1}catch(Da){return!1}if(Sr.tested)return Sr.writable;const e="lswt-".concat(Math.random()).concat(Math.random());try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Sr.tested=!0,Sr.writable=!0}catch(Da){Sr.tested=!0,Sr.writable=!1}return Sr.writable};const jr=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,630)).then((e=>{let{default:n}=e;return n(...t)}))}:fetch),function(){return t(...arguments)}},Er=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Nr=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch(r){return n}},Cr=async(e,t)=>{await e.removeItem(t)};class Tr{constructor(){this.promise=new Tr.promiseConstructor(((e,t)=>{this.resolve=e,this.reject=t}))}}function Pr(e){const t=e.split(".");if(3!==t.length)throw new dr("Invalid JWT structure");for(let n=0;n<t.length;n++)if(!Yn.test(t[n]))throw new dr("JWT not in base64url format");return{header:JSON.parse(vr(t[0])),payload:JSON.parse(vr(t[1])),signature:wr(t[2]),raw:{header:t[0],payload:t[1]}}}function Or(e){return("0"+e.toString(16)).substr(-2)}async function Rr(e){if(!("undefined"!==typeof crypto&&"undefined"!==typeof crypto.subtle&&"undefined"!==typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),n=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(n);return Array.from(r).map((e=>String.fromCharCode(e))).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Lr(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=function(){const e=new Uint32Array(56);if("undefined"===typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let n="";for(let r=0;r<56;r++)n+=e.charAt(Math.floor(Math.random()*t));return n}return crypto.getRandomValues(e),Array.from(e,Or).join("")}();let a=r;n&&(a+="/PASSWORD_RECOVERY"),await Er(e,"".concat(t,"-code-verifier"),a);const o=await Rr(r);return[o,r===o?"plain":"s256"]}Tr.promiseConstructor=Promise;const Ar=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const Ir=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function zr(e){if(!Ir.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Dr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const Mr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ur=[502,503,504];async function Fr(e){var t,n;if(!("object"===typeof(n=e)&&null!==n&&"status"in n&&"ok"in n&&"json"in n&&"function"===typeof n.json))throw new lr(Mr(e),0);if(Ur.includes(e.status))throw new lr(Mr(e),e.status);let r,a;try{r=await e.json()}catch(Da){throw new tr(Mr(Da),Da)}const o=function(e){const t=e.headers.get(Jn);if(!t)return null;if(!t.match(Ar))return null;try{return new Date("".concat(t,"T00:00:00.0Z"))}catch(Da){return null}}(e);if(o&&o.getTime()>=Qn.timestamp&&"object"===typeof r&&r&&"string"===typeof r.code?a=r.code:"object"===typeof r&&r&&"string"===typeof r.error_code&&(a=r.error_code),a){if("weak_password"===a)throw new ur(Mr(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===a)throw new rr}else if("object"===typeof r&&r&&"object"===typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce(((e,t)=>e&&"string"===typeof t),!0))throw new ur(Mr(r),e.status,r.weak_password.reasons);throw new er(Mr(r),e.status||500,a)}async function Br(e,t,n,r){var a;const o=Object.assign({},null===r||void 0===r?void 0:r.headers);o[Jn]||(o[Jn]=Qn.name),(null===r||void 0===r?void 0:r.jwt)&&(o.Authorization="Bearer ".concat(r.jwt));const i=null!==(a=null===r||void 0===r?void 0:r.query)&&void 0!==a?a:{};(null===r||void 0===r?void 0:r.redirectTo)&&(i.redirect_to=r.redirectTo);const s=Object.keys(i).length?"?"+new URLSearchParams(i).toString():"",l=await async function(e,t,n,r,a,o){const i=((e,t,n,r)=>{const a={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null===t||void 0===t?void 0:t.headers),a.body=JSON.stringify(r),Object.assign(Object.assign({},a),n))})(t,r,a,o);let s;try{s=await e(n,Object.assign({},i))}catch(Da){throw console.error(Da),new lr(Mr(Da),0)}s.ok||await Fr(s);if(null===r||void 0===r?void 0:r.noResolveJson)return s;try{return await s.json()}catch(Da){await Fr(Da)}}(e,t,n+s,{headers:o,noResolveJson:null===r||void 0===r?void 0:r.noResolveJson},{},null===r||void 0===r?void 0:r.body);return(null===r||void 0===r?void 0:r.xform)?null===r||void 0===r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function Hr(e){var t;let n=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:n,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function qr(e){const t=Hr(e);return!t.error&&e.weak_password&&"object"===typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"===typeof e.weak_password.message&&e.weak_password.reasons.reduce(((e,t)=>e&&"string"===typeof t),!0)&&(t.data.weak_password=e.weak_password),t}function Wr(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Vr(e){return{data:e,error:null}}function $r(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o}=e,i=Dr(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o},user:Object.assign({},i)},error:null}}function Kr(e){return e}const Gr=["global","local","others"];var Jr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};class Qr{constructor(e){let{url:t="",headers:n={},fetch:r}=e;this.url=t,this.headers=n,this.fetch=jr(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gr[0];if(Gr.indexOf(t)<0)throw new Error("@supabase/auth-js: Parameter scope must be one of ".concat(Gr.join(", ")));try{return await Br(this.fetch,"POST","".concat(this.url,"/logout?scope=").concat(t),{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(n){if(Zn(n))return{data:null,error:n};throw n}}async inviteUserByEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await Br(this.fetch,"POST","".concat(this.url,"/invite"),{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:Wr})}catch(n){if(Zn(n))return{data:{user:null},error:n};throw n}}async generateLink(e){try{const{options:t}=e,n=Jr(e,["options"]),r=Object.assign(Object.assign({},n),t);return"newEmail"in n&&(r.new_email=null===n||void 0===n?void 0:n.newEmail,delete r.newEmail),await Br(this.fetch,"POST","".concat(this.url,"/admin/generate_link"),{body:r,headers:this.headers,xform:$r,redirectTo:null===t||void 0===t?void 0:t.redirectTo})}catch(t){if(Zn(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Br(this.fetch,"POST","".concat(this.url,"/admin/users"),{body:e,headers:this.headers,xform:Wr})}catch(t){if(Zn(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,n,r,a,o,i,s;try{const l={nextPage:null,lastPage:0,total:0},c=await Br(this.fetch,"GET","".concat(this.url,"/admin/users"),{headers:this.headers,noResolveJson:!0,query:{page:null!==(n=null===(t=null===e||void 0===e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==n?n:"",per_page:null!==(a=null===(r=null===e||void 0===e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==a?a:""},xform:Kr});if(c.error)throw c.error;const u=await c.json(),d=null!==(o=c.headers.get("x-total-count"))&&void 0!==o?o:0,h=null!==(s=null===(i=c.headers.get("link"))||void 0===i?void 0:i.split(","))&&void 0!==s?s:[];return h.length>0&&(h.forEach((e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),n=JSON.parse(e.split(";")[1].split("=")[1]);l["".concat(n,"Page")]=t})),l.total=parseInt(d)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(Zn(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){zr(e);try{return await Br(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(e),{headers:this.headers,xform:Wr})}catch(t){if(Zn(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){zr(e);try{return await Br(this.fetch,"PUT","".concat(this.url,"/admin/users/").concat(e),{body:t,headers:this.headers,xform:Wr})}catch(n){if(Zn(n))return{data:{user:null},error:n};throw n}}async deleteUser(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];zr(e);try{return await Br(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(e),{headers:this.headers,body:{should_soft_delete:t},xform:Wr})}catch(n){if(Zn(n))return{data:{user:null},error:n};throw n}}async _listFactors(e){zr(e.userId);try{const{data:t,error:n}=await Br(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(e.userId,"/factors"),{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:n}}catch(t){if(Zn(t))return{data:null,error:t};throw t}}async _deleteFactor(e){zr(e.userId),zr(e.id);try{return{data:await Br(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(e.userId,"/factors/").concat(e.id),{headers:this.headers}),error:null}}catch(t){if(Zn(t))return{data:null,error:t};throw t}}}const Yr={getItem:e=>_r()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{_r()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{_r()&&globalThis.localStorage.removeItem(e)}};function Xr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}const Zr=!!(globalThis&&_r()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class ea extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class ta extends ea{}async function na(e,t,n){Zr&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout((()=>{r.abort(),Zr&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)}),t),await Promise.resolve().then((()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},(async r=>{if(!r){if(0===t)throw Zr&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new ta('Acquiring an exclusive Navigator LockManager lock "'.concat(e,'" immediately failed'));if(Zr)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(Da){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",Da)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}Zr&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await n()}finally{Zr&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))))}!function(){if("object"!==typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(Da){"undefined"!==typeof self&&(self.globalThis=self)}}();const ra={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Gn,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function aa(e,t,n){return await n()}class oa{constructor(e){var t,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=oa.nextInstanceID,oa.nextInstanceID+=1,this.instanceID>0&&xr()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},ra),e);if(this.logDebugMessages=!!r.debug,"function"===typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new Qr({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=jr(r.fetch),this.lock=r.lock||aa,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:xr()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=na:this.lock=aa,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:_r()?this.storage=Yr:(this.memoryStorage={},this.storage=Xr(this.memoryStorage)):(this.memoryStorage={},this.storage=Xr(this.memoryStorage)),xr()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(Da){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",Da)}null===(n=this.broadcastChannel)||void 0===n||n.addEventListener("message",(async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)}))}this.initialize()}_debug(){if(this.logDebugMessages){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.logger("GoTrueClient@".concat(this.instanceID," (").concat(Vn,") ").concat((new Date).toISOString()),...t)}return this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,(async()=>await this._initialize())))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},n=new URL(e);if(n.hash&&"#"===n.hash[0])try{new URLSearchParams(n.hash.substring(1)).forEach(((e,n)=>{t[n]=e}))}catch(Da){}return n.searchParams.forEach(((e,n)=>{t[n]=e})),t}(window.location.href);let n="none";if(this._isImplicitGrantCallback(t)?n="implicit":await this._isPKCECallback(t)&&(n="pkce"),xr()&&this.detectSessionInUrl&&"none"!==n){const{data:r,error:a}=await this._getSessionFromURL(t,n);if(a){if(this._debug("#_initialize()","error detecting session from URL",a),function(e){return Zn(e)&&"AuthImplicitGrantRedirectError"===e.name}(a)){const t=null===(e=a.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:a}}return await this._removeSession(),{error:a}}const{session:o,redirectType:i}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",i),await this._saveSession(o),setTimeout((async()=>{"recovery"===i?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return Zn(t)?{error:t}:{error:new tr("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,n,r;try{const a=await Br(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{data:null!==(n=null===(t=null===e||void 0===e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==n?n:{},gotrue_meta_security:{captcha_token:null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:Hr}),{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(Zn(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(e){var t,n,r;try{let a;if("email"in e){const{email:n,password:r,options:o}=e;let i=null,s=null;"pkce"===this.flowType&&([i,s]=await Lr(this.storage,this.storageKey)),a=await Br(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,redirectTo:null===o||void 0===o?void 0:o.emailRedirectTo,body:{email:n,password:r,data:null!==(t=null===o||void 0===o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null===o||void 0===o?void 0:o.captchaToken},code_challenge:i,code_challenge_method:s},xform:Hr})}else{if(!("phone"in e))throw new or("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:i}=e;a=await Br(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{phone:t,password:o,data:null!==(n=null===i||void 0===i?void 0:i.data)&&void 0!==n?n:{},channel:null!==(r=null===i||void 0===i?void 0:i.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null===i||void 0===i?void 0:i.captchaToken}},xform:Hr})}}const{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(Zn(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(e){try{let t;if("email"in e){const{email:n,password:r,options:a}=e;t=await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{email:n,password:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},xform:qr})}else{if(!("phone"in e))throw new or("You must provide either an email or phone number and a password");{const{phone:n,password:r,options:a}=e;t=await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{phone:n,password:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},xform:qr})}}const{data:n,error:r}=t;return r?{data:{user:null,session:null},error:r}:n&&n.session&&n.user?(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:r}):{data:{user:null,session:null},error:new ar}}catch(t){if(Zn(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,n,r,a;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(n=e.options)||void 0===n?void 0:n.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(a=e.options)||void 0===a?void 0:a.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,(async()=>this._exchangeCodeForSession(e)))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error('@supabase/auth-js: Unsupported chain "'.concat(t,'"'))}async signInWithSolana(e){var t,n,r,a,o,i,s,l,c,u,d,h;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:d,wallet:h,statement:m,options:g}=e;let v;if(xr())if("object"===typeof h)v=h;else{const e=window;if(!("solana"in e)||"object"!==typeof e.solana||!("signIn"in e.solana&&"function"===typeof e.solana.signIn||"signMessage"in e.solana&&"function"===typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");v=e.solana}else{if("object"!==typeof h||!(null===g||void 0===g?void 0:g.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");v=h}const y=new URL(null!==(t=null===g||void 0===g?void 0:g.url)&&void 0!==t?t:window.location.href);if("signIn"in v&&v.signIn){const e=await v.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null===g||void 0===g?void 0:g.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),m?{statement:m}:null));let t;if(Array.isArray(e)&&e[0]&&"object"===typeof e[0])t=e[0];else{if(!(e&&"object"===typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"===typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"===typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in v)||"function"!==typeof v.signMessage||!("publicKey"in v)||"object"!==typeof v||!v.publicKey||!("toBase58"in v.publicKey)||"function"!==typeof v.publicKey.toBase58)throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=["".concat(y.host," wants you to sign in with your Solana account:"),v.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1","URI: ".concat(y.href),"Issued At: ".concat(null!==(r=null===(n=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===n?void 0:n.issuedAt)&&void 0!==r?r:(new Date).toISOString()),...(null===(a=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===a?void 0:a.notBefore)?["Not Before: ".concat(g.signInWithSolana.notBefore)]:[],...(null===(o=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===o?void 0:o.expirationTime)?["Expiration Time: ".concat(g.signInWithSolana.expirationTime)]:[],...(null===(i=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===i?void 0:i.chainId)?["Chain ID: ".concat(g.signInWithSolana.chainId)]:[],...(null===(s=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===s?void 0:s.nonce)?["Nonce: ".concat(g.signInWithSolana.nonce)]:[],...(null===(l=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===l?void 0:l.requestId)?["Request ID: ".concat(g.signInWithSolana.requestId)]:[],...(null===(u=null===(c=null===g||void 0===g?void 0:g.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...g.signInWithSolana.resources.map((e=>"- ".concat(e)))]:[]].join("\n");const e=await v.signMessage((new TextEncoder).encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:n}=await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=web3"),{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:kr(p)},(null===(d=e.options)||void 0===d?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null===(h=e.options)||void 0===h?void 0:h.captchaToken}}:null),xform:Hr});if(n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:n}):{data:{user:null,session:null},error:new ar}}catch(m){if(Zn(m))return{data:{user:null,session:null},error:m};throw m}}async _exchangeCodeForSession(e){const t=await Nr(this.storage,"".concat(this.storageKey,"-code-verifier")),[n,r]=(null!==t&&void 0!==t?t:"").split("/");try{const{data:t,error:a}=await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=pkce"),{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:Hr});if(await Cr(this.storage,"".concat(this.storageKey,"-code-verifier")),a)throw a;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!==r&&void 0!==r?r:null}),error:a}):{data:{user:null,session:null,redirectType:null},error:new ar}}catch(a){if(Zn(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(e){try{const{options:t,provider:n,token:r,access_token:a,nonce:o}=e,i=await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=id_token"),{headers:this.headers,body:{provider:n,id_token:r,access_token:a,nonce:o,gotrue_meta_security:{captcha_token:null===t||void 0===t?void 0:t.captchaToken}},xform:Hr}),{data:s,error:l}=i;return l?{data:{user:null,session:null},error:l}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:s,error:l}):{data:{user:null,session:null},error:new ar}}catch(t){if(Zn(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,n,r,a,o;try{if("email"in e){const{email:r,options:a}=e;let o=null,i=null;"pkce"===this.flowType&&([o,i]=await Lr(this.storage,this.storageKey));const{error:s}=await Br(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{email:r,data:null!==(t=null===a||void 0===a?void 0:a.data)&&void 0!==t?t:{},create_user:null===(n=null===a||void 0===a?void 0:a.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken},code_challenge:o,code_challenge_method:i},redirectTo:null===a||void 0===a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){const{phone:t,options:n}=e,{data:i,error:s}=await Br(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{phone:t,data:null!==(r=null===n||void 0===n?void 0:n.data)&&void 0!==r?r:{},create_user:null===(a=null===n||void 0===n?void 0:n.shouldCreateUser)||void 0===a||a,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken},channel:null!==(o=null===n||void 0===n?void 0:n.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null===i||void 0===i?void 0:i.message_id},error:s}}throw new or("You must provide either an email or phone number.")}catch(i){if(Zn(i))return{data:{user:null,session:null},error:i};throw i}}async verifyOtp(e){var t,n;try{let r,a;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,a=null===(n=e.options)||void 0===n?void 0:n.captchaToken);const{data:o,error:i}=await Br(this.fetch,"POST","".concat(this.url,"/verify"),{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:a}}),redirectTo:r,xform:Hr});if(i)throw i;if(!o)throw new Error("An error occurred on token verification.");const s=o.session,l=o.user;return(null===s||void 0===s?void 0:s.access_token)&&(await this._saveSession(s),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(r){if(Zn(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,n,r;try{let a=null,o=null;return"pkce"===this.flowType&&([a,o]=await Lr(this.storage,this.storageKey)),await Br(this.fetch,"POST","".concat(this.url,"/sso"),{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(n=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==n?n:void 0}),(null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:a,code_challenge_method:o}),headers:this.headers,xform:Vr})}catch(a){if(Zn(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._reauthenticate()))}async _reauthenticate(){try{return await this._useSession((async e=>{const{data:{session:t},error:n}=e;if(n)throw n;if(!t)throw new rr;const{error:r}=await Br(this.fetch,"GET","".concat(this.url,"/reauthenticate"),{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(Zn(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t="".concat(this.url,"/resend");if("email"in e){const{email:n,type:r,options:a}=e,{error:o}=await Br(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},redirectTo:null===a||void 0===a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:n,type:r,options:a}=e,{data:o,error:i}=await Br(this.fetch,"POST",t,{headers:this.headers,body:{phone:n,type:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}}});return{data:{user:null,session:null,messageId:null===o||void 0===o?void 0:o.message_id},error:i}}throw new or("You must provide either an email or phone number and a type")}catch(t){if(Zn(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,(async()=>this._useSession((async e=>e))))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await n}catch(Da){}})()),n}return await this.lock("lock:".concat(this.storageKey),e,(async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(Da){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await Nr(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const n=!!e.expires_at&&1e3*e.expires_at-Date.now()<Kn;if(this._debug("#__loadSession()","session has".concat(n?"":" not"," expired"),"expires_at",e.expires_at),!n){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,n,r)=>(t||"user"!==n||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,n,r))})}return{data:{session:e},error:null}}const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{session:null},error:a}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,(async()=>await this._getUser()))}async _getUser(e){try{return e?await Br(this.fetch,"GET","".concat(this.url,"/user"),{headers:this.headers,jwt:e,xform:Wr}):await this._useSession((async e=>{var t,n,r;const{data:a,error:o}=e;if(o)throw o;return(null===(t=a.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Br(this.fetch,"GET","".concat(this.url,"/user"),{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0,xform:Wr}):{data:{user:null},error:new rr}}))}catch(t){if(Zn(t))return function(e){return Zn(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Cr(this.storage,"".concat(this.storageKey,"-code-verifier"))),{data:{user:null},error:t};throw t}}async updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._updateUser(e,t)))}async _updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await this._useSession((async n=>{const{data:r,error:a}=n;if(a)throw a;if(!r.session)throw new rr;const o=r.session;let i=null,s=null;"pkce"===this.flowType&&null!=e.email&&([i,s]=await Lr(this.storage,this.storageKey));const{data:l,error:c}=await Br(this.fetch,"PUT","".concat(this.url,"/user"),{headers:this.headers,redirectTo:null===t||void 0===t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:i,code_challenge_method:s}),jwt:o.access_token,xform:Wr});if(c)throw c;return o.user=l.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}}))}catch(n){if(Zn(n))return{data:{user:null},error:n};throw n}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._setSession(e)))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new rr;const t=Date.now()/1e3;let n=t,r=!0,a=null;const{payload:o}=Pr(e.access_token);if(o.exp&&(n=o.exp,r=n<=t),r){const{session:t,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{user:null,session:null},error:n};if(!t)return{data:{user:null,session:null},error:null};a=t}else{const{data:r,error:o}=await this._getUser(e.access_token);if(o)throw o;a={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:n-t,expires_at:n},await this._saveSession(a),await this._notifyAllSubscribers("SIGNED_IN",a)}return{data:{user:a.user,session:a},error:null}}catch(t){if(Zn(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._refreshSession(e)))}async _refreshSession(e){try{return await this._useSession((async t=>{var n;if(!e){const{data:r,error:a}=t;if(a)throw a;e=null!==(n=r.session)&&void 0!==n?n:void 0}if(!(null===e||void 0===e?void 0:e.refresh_token))throw new rr;const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{user:null,session:null},error:a}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(Zn(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!xr())throw new ir("No browser detected.");if(e.error||e.error_description||e.error_code)throw new ir(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new sr("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new ir("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new sr("No code detected.");const{data:t,error:n}=await this._exchangeCodeForSession(e.code);if(n)throw n;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:r,access_token:a,refresh_token:o,expires_in:i,expires_at:s,token_type:l}=e;if(!a||!i||!o||!l)throw new ir("No session defined in URL");const c=Math.round(Date.now()/1e3),u=parseInt(i);let d=c+u;s&&(d=parseInt(s));const h=d-c;1e3*h<=$n&&console.warn("@supabase/gotrue-js: Session as retrieved from URL expires in ".concat(h,"s, should have been closer to ").concat(u,"s"));const f=d-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,d,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,d,c);const{data:p,error:m}=await this._getUser(a);if(m)throw m;const g={provider_token:n,provider_refresh_token:r,access_token:a,expires_in:u,expires_at:d,refresh_token:o,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:g,redirectType:e.type},error:null}}catch(n){if(Zn(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Nr(this.storage,"".concat(this.storageKey,"-code-verifier"));return!(!e.code||!t)}async signOut(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._signOut(e)))}async _signOut(){let{scope:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this._useSession((async t=>{var n;const{data:r,error:a}=t;if(a)return{error:a};const o=null===(n=r.session)||void 0===n?void 0:n.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return Zn(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Cr(this.storage,"".concat(this.storageKey,"-code-verifier"))),{error:null}}))}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),n={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(async()=>{await this.initializePromise,await this._acquireLock(-1,(async()=>{this._emitInitialSession(t)}))})(),{data:{subscription:n}}}async _emitInitialSession(e){return await this._useSession((async t=>{var n,r;try{const{data:{session:r},error:a}=t;if(a)throw a;await(null===(n=this.stateChangeEmitters.get(e))||void 0===n?void 0:n.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(a){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",a),console.error(a)}}))}async resetPasswordForEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null;"pkce"===this.flowType&&([n,r]=await Lr(this.storage,this.storageKey,!0));try{return await Br(this.fetch,"POST","".concat(this.url,"/recover"),{body:{email:e,code_challenge:n,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(a){if(Zn(a))return{data:null,error:a};throw a}}async getUserIdentities(){var e;try{const{data:t,error:n}=await this.getUser();if(n)throw n;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Zn(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:n,error:r}=await this._useSession((async t=>{var n,r,a,o,i;const{data:s,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider("".concat(this.url,"/user/identities/authorize"),e.provider,{redirectTo:null===(n=e.options)||void 0===n?void 0:n.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(a=e.options)||void 0===a?void 0:a.queryParams,skipBrowserRedirect:!0});return await Br(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(i=null===(o=s.session)||void 0===o?void 0:o.access_token)&&void 0!==i?i:void 0})}));if(r)throw r;return xr()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null===n||void 0===n?void 0:n.url),{data:{provider:e.provider,url:null===n||void 0===n?void 0:n.url},error:null}}catch(n){if(Zn(n))return{data:{provider:e.provider,url:null},error:n};throw n}}async unlinkIdentity(e){try{return await this._useSession((async t=>{var n,r;const{data:a,error:o}=t;if(o)throw o;return await Br(this.fetch,"DELETE","".concat(this.url,"/user/identities/").concat(e.identity_id),{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0})}))}catch(t){if(Zn(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t="#_refreshAccessToken(".concat(e.substring(0,5),"...)");this._debug(t,"begin");try{const a=Date.now();return await(n=async n=>(n>0&&await async function(e){return await new Promise((t=>{setTimeout((()=>t(null)),e)}))}(200*Math.pow(2,n-1)),this._debug(t,"refreshing attempt",n),await Br(this.fetch,"POST","".concat(this.url,"/token?grant_type=refresh_token"),{body:{refresh_token:e},headers:this.headers,xform:Hr})),r=(e,t)=>{const n=200*Math.pow(2,e);return t&&cr(t)&&Date.now()+n-a<$n},new Promise(((e,t)=>{(async()=>{for(let a=0;a<1/0;a++)try{const t=await n(a);if(!r(a,null,t))return void e(t)}catch(Da){if(!r(a,Da))return void t(Da)}})()})))}catch(a){if(this._debug(t,"error",a),Zn(a))return{data:{session:null,user:null},error:a};throw a}finally{this._debug(t,"end")}var n,r}_isValidSession(e){return"object"===typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const n=await this._getUrlForProvider("".concat(this.url,"/authorize"),e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",n),xr()&&!t.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const n=await Nr(this.storage,this.storageKey);if(this._debug(t,"session from storage",n),!this._isValidSession(n))return this._debug(t,"session is not valid"),void(null!==n&&await this._removeSession());const r=1e3*(null!==(e=n.expires_at)&&void 0!==e?e:1/0)-Date.now()<Kn;if(this._debug(t,"session has".concat(r?"":" not"," expired with margin of ").concat(Kn,"s")),r){if(this.autoRefreshToken&&n.refresh_token){const{error:e}=await this._callRefreshToken(n.refresh_token);e&&(console.error(e),cr(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){return this._debug(t,"error",n),void console.error(n)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,n;if(!e)throw new rr;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r="#_callRefreshToken(".concat(e.substring(0,5),"...)");this._debug(r,"begin");try{this.refreshingDeferred=new Tr;const{data:t,error:n}=await this._refreshAccessToken(e);if(n)throw n;if(!t.session)throw new rr;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(a){if(this._debug(r,"error",a),Zn(a)){const e={session:null,error:a};return cr(a)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(n=this.refreshingDeferred)||void 0===n||n.reject(a),a}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const r="#_notifyAllSubscribers(".concat(e,")");this._debug(r,"begin",t,"broadcast = ".concat(n));try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],a=Array.from(this.stateChangeEmitters.values()).map((async n=>{try{await n.callback(e,t)}catch(Da){r.push(Da)}}));if(await Promise.all(a),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Er(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Cr(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&xr()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(Da){console.error("removing visibilitychange callback failed",Da)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval((()=>this._autoRefreshTokenTick()),$n);this.autoRefreshTicker=e,e&&"object"===typeof e&&"function"===typeof e.unref?e.unref():"undefined"!==typeof Deno&&"function"===typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout((async()=>{await this.initializePromise,await this._autoRefreshTokenTick()}),0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,(async()=>{try{const e=Date.now();try{return await this._useSession((async t=>{const{data:{session:n}}=t;if(!n||!n.refresh_token||!n.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*n.expires_at-e)/$n);this._debug("#_autoRefreshTokenTick()","access token expires in ".concat(r," ticks, a tick lasts ").concat($n,"ms, refresh threshold is ").concat(3," ticks")),r<=3&&await this._callRefreshToken(n.refresh_token)}))}catch(Da){console.error("Auto refresh tick failed with error. This is likely a transient error.",Da)}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(Da){if(!(Da.isAcquireTimeout||Da instanceof ea))throw Da;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!xr()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t="#_onVisibilityChanged(".concat(e,")");this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,(async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,n){const r=["provider=".concat(encodeURIComponent(t))];if((null===n||void 0===n?void 0:n.redirectTo)&&r.push("redirect_to=".concat(encodeURIComponent(n.redirectTo))),(null===n||void 0===n?void 0:n.scopes)&&r.push("scopes=".concat(encodeURIComponent(n.scopes))),"pkce"===this.flowType){const[e,t]=await Lr(this.storage,this.storageKey),n=new URLSearchParams({code_challenge:"".concat(encodeURIComponent(e)),code_challenge_method:"".concat(encodeURIComponent(t))});r.push(n.toString())}if(null===n||void 0===n?void 0:n.queryParams){const e=new URLSearchParams(n.queryParams);r.push(e.toString())}return(null===n||void 0===n?void 0:n.skipBrowserRedirect)&&r.push("skip_http_redirect=".concat(n.skipBrowserRedirect)),"".concat(e,"?").concat(r.join("&"))}async _unenroll(e){try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await Br(this.fetch,"DELETE","".concat(this.url,"/factors/").concat(e.factorId),{headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token})}))}catch(t){if(Zn(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession((async t=>{var n,r;const{data:a,error:o}=t;if(o)return{data:null,error:o};const i=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:s,error:l}=await Br(this.fetch,"POST","".concat(this.url,"/factors"),{body:i,headers:this.headers,jwt:null===(n=null===a||void 0===a?void 0:a.session)||void 0===n?void 0:n.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null===s||void 0===s?void 0:s.totp)||void 0===r?void 0:r.qr_code)&&(s.totp.qr_code="data:image/svg+xml;utf-8,".concat(s.totp.qr_code)),{data:s,error:null})}))}catch(t){if(Zn(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;if(a)return{data:null,error:a};const{data:o,error:i}=await Br(this.fetch,"POST","".concat(this.url,"/factors/").concat(e.factorId,"/verify"),{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token});return i?{data:null,error:i}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:i})}))}catch(t){if(Zn(t))return{data:null,error:t};throw t}}))}async _challenge(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await Br(this.fetch,"POST","".concat(this.url,"/factors/").concat(e.factorId,"/challenge"),{body:{channel:e.channel},headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token})}))}catch(t){if(Zn(t))return{data:null,error:t};throw t}}))}async _challengeAndVerify(e){const{data:t,error:n}=await this._challenge({factorId:e.factorId});return n?{data:null,error:n}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const n=(null===e||void 0===e?void 0:e.factors)||[],r=n.filter((e=>"totp"===e.factor_type&&"verified"===e.status)),a=n.filter((e=>"phone"===e.factor_type&&"verified"===e.status));return{data:{all:n,totp:r,phone:a},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,(async()=>await this._useSession((async e=>{var t,n;const{data:{session:r},error:a}=e;if(a)return{data:null,error:a};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Pr(r.access_token);let i=null;o.aal&&(i=o.aal);let s=i;(null!==(n=null===(t=r.user.factors)||void 0===t?void 0:t.filter((e=>"verified"===e.status)))&&void 0!==n?n:[]).length>0&&(s="aal2");return{data:{currentLevel:i,nextLevel:s,currentAuthenticationMethods:o.amr||[]},error:null}}))))}async fetchJwk(e){let t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]}).keys.find((t=>t.kid===e));if(t)return t;if(t=this.jwks.keys.find((t=>t.kid===e)),t&&this.jwks_cached_at+6e5>Date.now())return t;const{data:n,error:r}=await Br(this.fetch,"GET","".concat(this.url,"/.well-known/jwks.json"),{headers:this.headers});if(r)throw r;if(!n.keys||0===n.keys.length)throw new dr("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),t=n.keys.find((t=>t.kid===e)),!t)throw new dr("No matching signing key found in JWKS");return t}async getClaims(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]};try{let n=e;if(!n){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}const{header:r,payload:a,signature:o,raw:{header:i,payload:s}}=Pr(n);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(a.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:a,header:r,signature:o},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),c=await this.fetchJwk(r.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!await crypto.subtle.verify(l,u,o,function(e){const t=[];return br(e,(e=>t.push(e))),new Uint8Array(t)}("".concat(i,".").concat(s))))throw new dr("Invalid JWT signature");return{data:{claims:a,header:r,signature:o},error:null}}catch(n){if(Zn(n))return{data:null,error:n};throw n}}}oa.nextInstanceID=0;const ia=oa;class sa extends ia{constructor(e){super(e)}}var la=function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(Da){o(Da)}}function s(e){try{l(r.throw(e))}catch(Da){o(Da)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class ca{constructor(e,t,n){var r,a,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const i=(s=e).endsWith("/")?s:s+"/";var s;const l=new URL(i);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c="sb-".concat(l.hostname.split(".")[0],"-auth-token"),u=function(e,t){var n,r;const{db:a,auth:o,realtime:i,global:s}=e,{db:l,auth:c,realtime:u,global:d}=t,h={db:Object.assign(Object.assign({},l),a),auth:Object.assign(Object.assign({},c),o),realtime:Object.assign(Object.assign({},u),i),global:Object.assign(Object.assign(Object.assign({},d),s),{headers:Object.assign(Object.assign({},null!==(n=null===d||void 0===d?void 0:d.headers)&&void 0!==n?n:{}),null!==(r=null===s||void 0===s?void 0:s.headers)&&void 0!==r?r:{})}),accessToken:()=>Wn(this,void 0,void 0,(function*(){return""}))};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!==n&&void 0!==n?n:{},{db:Dn,realtime:Un,auth:Object.assign(Object.assign({},Mn),{storageKey:c}),global:zn});this.storageKey=null!==(r=u.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(a=u.global.headers)&&void 0!==a?a:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error("@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.".concat(String(t)," is not possible"))}})):this.auth=this._initSupabaseAuthClient(null!==(o=u.auth)&&void 0!==o?o:{},this.headers,u.global.fetch),this.fetch=qn(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new Tt(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new Nt(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new An(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.rest.rpc(e,t,n)}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return la(this,void 0,void 0,(function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return null!==(t=null===(e=n.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null}))}_initSupabaseAuthClient(e,t,n){let{autoRefreshToken:r,persistSession:a,detectSessionInUrl:o,storage:i,storageKey:s,flowType:l,lock:c,debug:u}=e;const d={Authorization:"Bearer ".concat(this.supabaseKey),apikey:"".concat(this.supabaseKey)};return new sa({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),t),storageKey:s,autoRefreshToken:r,persistSession:a,detectSessionInUrl:o,storage:i,flowType:l,lock:c,debug:u,fetch:n,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new dn(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null===e||void 0===e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange(((e,t)=>{this._handleTokenChanged(e,"CLIENT",null===t||void 0===t?void 0:t.access_token)}))}_handleTokenChanged(e,t,n){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===n?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=n}}const ua=((e,t,n)=>new ca(e,t,n))("https://qbyyutebrgpxngvwenkd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieXl1dGVicmdweG5ndndlbmtkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTA1NTMsImV4cCI6MjA2NTI4NjU1M30.eO8Wd0ZOqtXgvQ3BuedmSPmYVpbG3V-AXvgufLns6yY"),da="/",ha="/dashboard",fa="/marketplace",pa="/messages",ma="/profile",ga=e=>{const t="/my-tutor-app/react-version";return"/"===e?t:"".concat(t).concat(e)};var va=n(579);const ya=e=>{let{onLoginClick:t}=e;return(0,va.jsx)("nav",{className:"responsive-nav navbar",children:(0,va.jsx)("div",{className:"responsive-container nav-container",children:(0,va.jsxs)("div",{className:"nav-content",children:[(0,va.jsx)("div",{className:"nav-left",children:(0,va.jsx)(pt,{to:da,className:"nav-logo",children:"IndianTutors"})}),(0,va.jsxs)("div",{className:"nav-right nav-links",children:[(0,va.jsx)(pt,{to:fa,className:"nav-link",children:"Find a Teacher"}),(0,va.jsx)("button",{className:"btn btn-primary nav-btn login-btn",onClick:t,children:"Log in"})]})]})})})},ba=e=>{let{onClose:t}=e;(0,r.useEffect)((()=>{const e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[t]);const n=e=>{const t=document.createElement("div");t.style.cssText="\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #38B000;\n      color: white;\n      padding: 15px 20px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-family: Inter, sans-serif;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n    ",t.textContent=e,document.body.appendChild(t),setTimeout((()=>{document.body.contains(t)&&document.body.removeChild(t)}),3e3)},a=e=>{const t=document.createElement("div");t.style.cssText="\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #DC2626;\n      color: white;\n      padding: 15px 20px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-family: Inter, sans-serif;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n    ",t.textContent=e,document.body.appendChild(t),setTimeout((()=>{document.body.contains(t)&&document.body.removeChild(t)}),5e3)};return(0,va.jsx)("div",{className:"modal-overlay active",onClick:e=>{e.target===e.currentTarget&&t()},children:(0,va.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,va.jsxs)("div",{className:"modal-header",children:[(0,va.jsx)("h2",{className:"modal-title",children:"Welcome to IndianTutors"}),(0,va.jsx)("button",{className:"modal-close",onClick:t,"aria-label":"Close modal",children:(0,va.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,va.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,va.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,va.jsxs)("div",{className:"modal-body",children:[(0,va.jsx)("p",{className:"modal-subtitle",children:"Start your language learning journey today"}),(0,va.jsxs)("button",{className:"btn btn-primary google-login-btn btn-full-mobile",onClick:async()=>{console.log("Initiating Supabase Google login...");try{const{data:e,error:r}=await ua.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin).concat(ga(ha))}});r?(console.error("Supabase auth error:",r),a("Login failed: "+r.message)):(console.log("Google login initiated successfully"),t(),n("Redirecting to Google..."))}catch(e){console.error("Login error:",e),a("Login failed. Please try again.")}},children:[(0,va.jsx)("div",{className:"google-icon-wrapper",children:(0,va.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",children:[(0,va.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,va.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,va.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,va.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})}),(0,va.jsx)("span",{children:"Continue with Google"})]}),(0,va.jsx)("div",{className:"modal-divider",children:(0,va.jsx)("span",{children:"or"})}),(0,va.jsx)("div",{className:"alternative-options",children:(0,va.jsx)("p",{className:"coming-soon",children:"Email login coming soon"})})]}),(0,va.jsx)("div",{className:"modal-footer",children:(0,va.jsxs)("p",{className:"terms-text",children:["By continuing, you agree to our",(0,va.jsx)("a",{href:"#",className:"terms-link",children:"Terms of Service"})," and",(0,va.jsx)("a",{href:"#",className:"terms-link",children:"Privacy Policy"})]})})]})})},wa=()=>{const[e,t]=(0,r.useState)(!1),[n,a]=(0,r.useState)(null),o=pe();(0,r.useEffect)((()=>{const e=new URLSearchParams(window.location.search).get("redirect");if(e)return window.history.replaceState({},"",window.location.pathname),void o(e);i();const{data:{subscription:t}}=ua.auth.onAuthStateChange(((e,t)=>{var n;a(null!==(n=null===t||void 0===t?void 0:t.user)&&void 0!==n?n:null)}));return()=>t.unsubscribe()}),[o]);const i=async()=>{const{data:{session:e}}=await ua.auth.getSession();e&&(a(e.user),o(ha))},s=()=>{t(!0),document.body.style.overflow="hidden"};return(0,va.jsxs)("div",{className:"landing-page",children:[(0,va.jsx)(ya,{onLoginClick:s}),(0,va.jsx)("section",{className:"hero",children:(0,va.jsxs)("div",{className:"responsive-container hero-container",children:[(0,va.jsxs)("div",{className:"hero-content",children:[(0,va.jsx)("h1",{className:"hero-title",children:"Become fluent in any Indian language"}),(0,va.jsxs)("ul",{className:"hero-features",children:[(0,va.jsxs)("li",{children:[(0,va.jsx)("span",{className:"feature-icon",children:"\ud83d\udcf9"}),"1-on-1 video lessons"]}),(0,va.jsxs)("li",{children:[(0,va.jsx)("span",{className:"feature-icon",children:"\ud83c\udf93"}),"Certified tutors"]}),(0,va.jsxs)("li",{children:[(0,va.jsx)("span",{className:"feature-icon",children:"\u23f0"}),"Flexible schedules and prices"]})]}),(0,va.jsxs)("button",{className:"btn btn-primary cta-button btn-full-mobile",onClick:s,children:[(0,va.jsx)("span",{className:"google-icon",children:(0,va.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",children:[(0,va.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,va.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,va.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,va.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})}),"Start Now with Google"]})]}),(0,va.jsx)("div",{className:"hero-image",children:(0,va.jsx)("div",{className:"hero-illustration",children:(0,va.jsxs)("div",{className:"illustration-card",children:[(0,va.jsxs)("div",{className:"card-header",children:[(0,va.jsx)("div",{className:"avatar"}),(0,va.jsxs)("div",{className:"tutor-info",children:[(0,va.jsx)("div",{className:"tutor-name"}),(0,va.jsx)("div",{className:"tutor-lang"})]})]}),(0,va.jsx)("div",{className:"card-content",children:(0,va.jsx)("div",{className:"lesson-preview"})})]})})})]})}),(0,va.jsx)("section",{className:"languages",children:(0,va.jsxs)("div",{className:"responsive-container languages-container",children:[(0,va.jsx)("h2",{className:"languages-title",children:"Popular Indian Languages"}),(0,va.jsxs)("div",{className:"responsive-grid grid-4 grid-mobile-2 languages-grid",children:[(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Hindi"}),(0,va.jsx)("span",{className:"tutor-count",children:"1,247 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Tamil"}),(0,va.jsx)("span",{className:"tutor-count",children:"892 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Bengali"}),(0,va.jsx)("span",{className:"tutor-count",children:"634 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Telugu"}),(0,va.jsx)("span",{className:"tutor-count",children:"578 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Marathi"}),(0,va.jsx)("span",{className:"tutor-count",children:"423 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Gujarati"}),(0,va.jsx)("span",{className:"tutor-count",children:"356 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Kannada"}),(0,va.jsx)("span",{className:"tutor-count",children:"289 tutors"})]}),(0,va.jsxs)("div",{className:"language-pill",children:[(0,va.jsx)("span",{className:"language-name",children:"Malayalam"}),(0,va.jsx)("span",{className:"tutor-count",children:"234 tutors"})]})]})]})}),e&&(0,va.jsx)(ba,{onClose:()=>{t(!1),document.body.style.overflow=""}})]})},ka=e=>{let{tutor:t,onContact:n}=e;const r=t.photo_url||"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(t.name),"&background=6366f1&color=fff&size=80"),a=t.languages_spoken&&t.languages_spoken.length>0?"string"===typeof t.languages_spoken?JSON.parse(t.languages_spoken):t.languages_spoken:[{language:t.native_language||t.language,proficiency:"Native"}],o=t.tags&&t.tags.length>0?"string"===typeof t.tags?JSON.parse(t.tags):t.tags:["Conversational","Grammar"],i=t.country_flag||"\ud83c\uddee\ud83c\uddf3",s=t.total_students||Math.floor(50*Math.random()+10),l=t.total_lessons||Math.floor(200*Math.random()+50),c=t.is_professional||!1,u=t.rating||4.5,d="\u2b50".repeat(Math.floor(u));return(0,va.jsx)("div",{className:"tutor-card",onClick:e=>{e.target.closest("button")||window.open("/my-tutor-app/react-version/profile?id=".concat(t.id),"_blank")},children:(0,va.jsx)("div",{className:"tutor-card-content",children:(0,va.jsxs)("div",{className:"tutor-card-layout",children:[(0,va.jsx)("div",{className:"tutor-avatar-section",children:(0,va.jsxs)("div",{className:"tutor-avatar-container",children:[(0,va.jsx)("img",{src:r,alt:t.name,className:"tutor-avatar"}),t.video_url&&(0,va.jsx)("div",{className:"video-indicator",children:(0,va.jsx)("svg",{className:"video-icon",fill:"currentColor",viewBox:"0 0 20 20",children:(0,va.jsx)("path",{d:"M8 5v10l8-5-8-5z"})})})]})}),(0,va.jsxs)("div",{className:"tutor-content",children:[(0,va.jsxs)("div",{className:"tutor-header",children:[(0,va.jsxs)("div",{className:"tutor-info",children:[(0,va.jsxs)("div",{className:"tutor-name-row",children:[(0,va.jsx)("h3",{className:"tutor-name",children:t.name}),(0,va.jsx)("span",{className:"country-flag",children:i}),c&&(0,va.jsx)("span",{className:"professional-badge",children:"Professional"})]}),(0,va.jsx)("p",{className:"tutor-headline",children:t.bio_headline||"".concat(t.native_language||t.language," Teacher")})]}),(0,va.jsxs)("div",{className:"tutor-pricing",children:[(0,va.jsxs)("div",{className:"price-amount",children:["\u20b9",t.rate]}),(0,va.jsx)("div",{className:"price-label",children:"per lesson"})]})]}),(0,va.jsxs)("div",{className:"language-badges",children:[(0,va.jsx)("span",{className:"language-badge primary",children:t.native_language||t.language}),a.slice(0,2).map(((e,t)=>(0,va.jsx)("span",{className:"language-badge secondary-".concat(t+1),children:e.language},t))),a.length>2&&(0,va.jsxs)("span",{className:"language-badge more",children:["+",a.length-2]})]}),(0,va.jsxs)("div",{className:"tutor-stats",children:[(0,va.jsxs)("div",{className:"stat-item",children:[(0,va.jsx)("span",{className:"rating-stars",children:d}),(0,va.jsx)("span",{className:"rating-value",children:u.toFixed(1)})]}),(0,va.jsxs)("div",{className:"stat-item",children:[(0,va.jsx)("svg",{className:"stat-icon",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,va.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})}),(0,va.jsxs)("span",{children:[s," students"]})]}),(0,va.jsxs)("div",{className:"stat-item",children:[(0,va.jsx)("svg",{className:"stat-icon",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,va.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),(0,va.jsxs)("span",{children:[l," lessons"]})]})]}),o.length>0&&(0,va.jsxs)("div",{className:"tutor-tags",children:[o.slice(0,2).map(((e,t)=>(0,va.jsx)("span",{className:"tag tag-".concat(t+1),children:e},t))),o.length>2&&(0,va.jsxs)("span",{className:"tag tag-more",children:["+",o.length-2]})]}),(0,va.jsxs)("div",{className:"tutor-actions",children:[(0,va.jsx)("button",{className:"action-btn contact-btn",onClick:e=>{e.stopPropagation(),n(t.user_id,t.name)},children:"Contact"}),(0,va.jsx)("button",{className:"action-btn book-btn",onClick:e=>{e.stopPropagation(),window.open("/my-tutor-app/react-version/profile?id=".concat(t.id),"_blank")},children:"Book Lesson"}),(0,va.jsx)("button",{className:"action-btn profile-btn",onClick:e=>{e.stopPropagation(),window.open("/my-tutor-app/react-version/profile?id=".concat(t.id),"_blank")},children:"Profile"})]})]})]})})})},xa=e=>{let{filters:t,onFilterChange:n,onClearFilters:a}=e;const[o,i]=(0,r.useState)(null);return(0,r.useEffect)((()=>()=>{o&&clearTimeout(o)}),[o]),(0,va.jsx)("div",{className:"search-filters",children:(0,va.jsxs)("div",{className:"filters-container",children:[(0,va.jsxs)("div",{className:"filter-group",children:[(0,va.jsx)("label",{className:"filter-label",children:"Language"}),(0,va.jsxs)("select",{className:"filter-select",value:t.language,onChange:e=>{n({language:e.target.value})},children:[(0,va.jsx)("option",{value:"",children:"All Languages"}),(0,va.jsx)("option",{value:"Hindi",children:"Hindi"}),(0,va.jsx)("option",{value:"Tamil",children:"Tamil"}),(0,va.jsx)("option",{value:"Bengali",children:"Bengali"}),(0,va.jsx)("option",{value:"Telugu",children:"Telugu"}),(0,va.jsx)("option",{value:"Marathi",children:"Marathi"}),(0,va.jsx)("option",{value:"Gujarati",children:"Gujarati"}),(0,va.jsx)("option",{value:"Kannada",children:"Kannada"}),(0,va.jsx)("option",{value:"Malayalam",children:"Malayalam"}),(0,va.jsx)("option",{value:"Punjabi",children:"Punjabi"}),(0,va.jsx)("option",{value:"Urdu",children:"Urdu"})]})]}),(0,va.jsxs)("div",{className:"filter-group",children:[(0,va.jsx)("label",{className:"filter-label",children:"Price Range"}),(0,va.jsxs)("select",{className:"filter-select",value:t.priceRange,onChange:e=>{n({priceRange:e.target.value})},children:[(0,va.jsx)("option",{value:"",children:"Any Price"}),(0,va.jsx)("option",{value:"0-300",children:"\u20b90 - \u20b9300"}),(0,va.jsx)("option",{value:"300-500",children:"\u20b9300 - \u20b9500"}),(0,va.jsx)("option",{value:"500-800",children:"\u20b9500 - \u20b9800"}),(0,va.jsx)("option",{value:"800-1200",children:"\u20b9800 - \u20b91200"}),(0,va.jsx)("option",{value:"1200+",children:"\u20b91200+"})]})]}),(0,va.jsxs)("div",{className:"filter-group search-group",children:[(0,va.jsx)("label",{className:"filter-label",children:"Search Tutors"}),(0,va.jsx)("input",{type:"text",className:"filter-input",placeholder:"Search by name...",defaultValue:t.searchTerm,onChange:e=>{const t=e.target.value;o&&clearTimeout(o);const r=setTimeout((()=>{n({searchTerm:t})}),300);i(r)}})]}),(0,va.jsx)("div",{className:"filter-group clear-group",children:(0,va.jsx)("button",{className:"clear-filters-btn",onClick:a,children:"Clear All"})})]})})},Sa=()=>(0,va.jsxs)("div",{className:"loading-container",children:[(0,va.jsx)("div",{className:"loading-spinner"}),(0,va.jsx)("p",{className:"loading-text",children:"Loading tutors..."})]}),_a=()=>{const[e,t]=(0,r.useState)([]),n=pe(),[a,o]=(0,r.useState)([]),[i,s]=(0,r.useState)(!0),[l,c]=(0,r.useState)(null),[d,h]=(0,r.useState)({language:"",priceRange:"",searchTerm:""});(0,r.useEffect)((()=>{f(),p()}),[]),(0,r.useEffect)((()=>{m()}),[d,e]);const f=async()=>{const{data:{session:e}}=await ua.auth.getSession();e?c(e.user):n(da)},p=async()=>{try{console.log("\ud83d\udd0d Loading tutors from database...");const{data:e,error:n}=await ua.from("tutors").select("*").eq("approved",!0).order("rating",{ascending:!1});if(n){console.error("\u274c Database error loading tutors:",n),console.log("\ud83d\udd04 Trying simpler query...");const{data:e,error:r}=await ua.from("tutors").select("*").limit(10);if(r)throw r;const a=(e||[]).filter((e=>!0===e.approved));t(a),console.log("\u2705 Loaded tutors with fallback method:",a.length)}else t(e||[]),console.log("\u2705 Loaded tutors successfully:",(null===e||void 0===e?void 0:e.length)||0)}catch(e){console.error("\u274c Error loading tutors:",e),g("Failed to load tutors: ".concat(e.message,". Please refresh the page or try again later.")),t([])}finally{s(!1)}},m=()=>{let t=e.filter((e=>{if(d.language&&(e.native_language||e.language)!==d.language)return!1;if(d.priceRange){const t=e.rate;if("0-300"===d.priceRange&&(t<0||t>300))return!1;if("300-500"===d.priceRange&&(t<300||t>500))return!1;if("500-800"===d.priceRange&&(t<500||t>800))return!1;if("800-1200"===d.priceRange&&(t<800||t>1200))return!1;if("1200+"===d.priceRange&&t<1200)return!1}if(d.searchTerm){if(!"".concat(e.name," ").concat(e.bio||""," ").concat(e.bio_headline||"").toLowerCase().includes(d.searchTerm.toLowerCase()))return!1}return!0}));o(t)},g=e=>{const t=document.createElement("div");t.className="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50",t.textContent=e,document.body.appendChild(t),setTimeout((()=>{document.body.contains(t)&&document.body.removeChild(t)}),5e3)},v=async(e,t)=>{try{const{data:{session:r}}=await ua.auth.getSession();if(!r)return alert("Please log in to contact teachers"),void n(da);const a=new window.SimpleMessaging(ua);await a.initialize();const o=await a.getOrCreateChat(e);alert("Chat created with ".concat(t,"! Chat ID: ").concat(o))}catch(r){console.error("Error contacting teacher:",r),alert("Failed to start conversation. Please try again.")}};return i?(0,va.jsx)(Sa,{}):(0,va.jsxs)("div",{className:"tutor-marketplace",children:[(0,va.jsxs)("div",{className:"marketplace-header",children:[(0,va.jsx)("h1",{className:"marketplace-title",children:"Find Your Perfect Language Tutor"}),(0,va.jsxs)("p",{className:"marketplace-subtitle",children:["Choose from ",(0,va.jsx)("span",{className:"tutor-count",children:a.length})," experienced tutors"]})]}),(0,va.jsx)(xa,{filters:d,onFilterChange:e=>{h((t=>u(u({},t),e)))},onClearFilters:()=>{h({language:"",priceRange:"",searchTerm:""})}}),(0,va.jsx)("div",{className:"marketplace-results",children:0===a.length?(0,va.jsxs)("div",{className:"no-results",children:[(0,va.jsx)("svg",{className:"no-results-icon",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,va.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"})}),(0,va.jsx)("h3",{className:"no-results-title",children:"No tutors found"}),(0,va.jsx)("p",{className:"no-results-text",children:"Try adjusting your filters or search terms."})]}):(0,va.jsx)("div",{className:"tutors-grid",children:a.map((e=>(0,va.jsx)(ka,{tutor:e,onContact:v},e.id)))})})]})},ja=e=>{let{user:t,unreadCount:n,onSignOut:r}=e;const a=()=>{var e,n;return t?(null===(e=t.user_metadata)||void 0===e?void 0:e.full_name)||(null===(n=t.email)||void 0===n?void 0:n.split("@")[0])||"Student":"Loading..."};return(0,va.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,va.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,va.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,va.jsx)("div",{className:"flex items-center",children:(0,va.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Student Dashboard"})}),(0,va.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,va.jsxs)(pt,{to:pa,className:"relative p-2 text-gray-600 hover:text-green-600 transition-colors",children:[(0,va.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,va.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"})}),n>0&&(0,va.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:n})]}),(0,va.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,va.jsx)("img",{className:"h-8 w-8 rounded-full",src:(()=>{var e;if(!t)return"";const n=a();return(null===(e=t.user_metadata)||void 0===e?void 0:e.avatar_url)||"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(n),"&background=3b82f6&color=fff")})(),alt:"Profile"}),(0,va.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a()})]}),(0,va.jsx)("button",{onClick:r,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Sign Out"})]})]})})})},Ea=e=>{let{lessons:t,availability:n,tutors:r}=e;const a=[{title:"Upcoming Lessons",value:(()=>{const e=new Date;return t.filter((t=>{if(!t.lesson_date||!t.start_time)return!1;try{return new Date(t.lesson_date+"T"+t.start_time)>e&&"confirmed"===t.status}catch(n){return!1}})).length})(),icon:"fas fa-calendar-check",bgColor:"bg-green-100",iconColor:"text-green-600"},{title:"Available Slots",value:n.length,icon:"fas fa-clock",bgColor:"bg-green-100",iconColor:"text-green-600"},{title:"Total Tutors",value:r.length,icon:"fas fa-users",bgColor:"bg-purple-100",iconColor:"text-purple-600"},{title:"Completed",value:t.filter((e=>"completed"===e.status)).length,icon:"fas fa-graduation-cap",bgColor:"bg-yellow-100",iconColor:"text-yellow-600"}];return(0,va.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:a.map(((e,t)=>(0,va.jsx)("div",{className:"bg-white rounded-xl shadow-sm p-6 border",children:(0,va.jsxs)("div",{className:"flex items-center",children:[(0,va.jsx)("div",{className:"p-2 ".concat(e.bgColor," rounded-lg"),children:(0,va.jsx)("i",{className:"".concat(e.icon," ").concat(e.iconColor)})}),(0,va.jsxs)("div",{className:"ml-4",children:[(0,va.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,va.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]})]})},t)))})},Na=e=>{let{tutors:t,availability:n,onBookingClick:r}=e;const a=e=>{const[t,n]=e.split(":"),r=new Date;return r.setHours(parseInt(t),parseInt(n)),r.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})};return 0===t.length?(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,va.jsx)("div",{className:"p-6 border-b",children:(0,va.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Available Tutors"})}),(0,va.jsx)("div",{className:"p-6",children:(0,va.jsxs)("div",{className:"text-center py-8",children:[(0,va.jsx)("i",{className:"fas fa-user-graduate text-4xl text-gray-300 mb-4"}),(0,va.jsx)("p",{className:"text-gray-500",children:"No tutors available at the moment"})]})})]}):(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,va.jsx)("div",{className:"p-6 border-b",children:(0,va.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Available Tutors"})}),(0,va.jsx)("div",{className:"p-6",children:t.map((e=>{const t=(o=e.id,n.filter((e=>e.tutor_id===o)));var o;const i=e.profile_picture||"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(e.name||e.email),"&background=3b82f6&color=fff");return(0,va.jsx)("div",{className:"tutor-card bg-gray-50 rounded-lg p-6 mb-4 border",children:(0,va.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,va.jsx)("img",{src:i,alt:e.name,className:"w-16 h-16 rounded-full object-cover"}),(0,va.jsxs)("div",{className:"flex-1",children:[(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name||e.email}),(0,va.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.email}),(0,va.jsxs)("div",{className:"mb-4",children:[(0,va.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Available Times:"}),(0,va.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:[t.slice(0,6).map((e=>{return(0,va.jsxs)("button",{onClick:()=>r(e),className:"available-slot px-3 py-2 rounded-lg text-xs font-medium bg-green-500 hover:bg-green-600 text-white transition-all duration-200 hover:transform hover:-translate-y-1",children:[(t=e.lesson_date,new Date(t).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}))," ",a(e.start_time)]},e.id);var t})),t.length>6&&(0,va.jsxs)("div",{className:"text-xs text-gray-500 flex items-center justify-center",children:["+",t.length-6," more"]})]})]})]})]})},e.id)}))})]})},Ca=e=>{let{lessons:t}=e;const n=e=>{const[t,n]=e.split(":"),r=new Date;return r.setHours(parseInt(t),parseInt(n)),r.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})},r=(()=>{const e=new Date;return t.filter((t=>{if(!t.lesson_date||!t.start_time)return!1;try{return new Date(t.lesson_date+"T"+t.start_time)>e&&"confirmed"===t.status}catch(n){return console.warn("Invalid lesson date/time:",t),!1}}))})();return(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,va.jsx)("div",{className:"p-6 border-b",children:(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"My Lessons"})}),(0,va.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:0===r.length?(0,va.jsxs)("div",{className:"text-center py-8",children:[(0,va.jsx)("i",{className:"fas fa-calendar-alt text-3xl text-gray-300 mb-3"}),(0,va.jsx)("p",{className:"text-gray-500 text-sm",children:"No upcoming lessons"}),(0,va.jsx)("p",{className:"text-xs text-gray-400 mt-2",children:"Approved lessons will appear here automatically"}),(0,va.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Total lessons found: ",t.length]})]}):r.map((e=>{const t=e.tutor_name||"Tutor",r="https://ui-avatars.com/api/?name=".concat(encodeURIComponent(t),"&background=ffffff&color=3b82f6");return(0,va.jsx)("div",{className:"booked-lesson rounded-lg p-4 mb-3 border-l-4 border-green-500 bg-gradient-to-r from-green-50 to-green-100",children:(0,va.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,va.jsx)("img",{src:r,alt:t,className:"w-10 h-10 rounded-full object-cover"}),(0,va.jsxs)("div",{className:"flex-1",children:[(0,va.jsx)("h4",{className:"font-medium text-sm text-gray-900",children:t}),(0,va.jsxs)("p",{className:"text-xs text-gray-600",children:[(a=e.lesson_date,new Date(a).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}))," at ",n(e.start_time)]}),(0,va.jsxs)("p",{className:"text-xs text-gray-500 capitalize",children:[e.lesson_type||"conversation_practice"," lesson - \u20b9",e.price||500,(0,va.jsx)("span",{className:"ml-2 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs",children:e.status||"confirmed"})]})]})]})},e.id);var a}))})]})},Ta=()=>{const e=[{title:"Find Tutors",description:"Browse available tutors",icon:"fas fa-search",color:"bg-blue-500 hover:bg-blue-600",route:fa},{title:"Messages",description:"Chat with your tutors",icon:"fas fa-comments",color:"bg-green-500 hover:bg-green-600",route:pa},{title:"Profile",description:"Update your profile",icon:"fas fa-user",color:"bg-purple-500 hover:bg-purple-600",route:ma}];return(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,va.jsx)("div",{className:"p-6 border-b",children:(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),(0,va.jsx)("div",{className:"p-6",children:(0,va.jsx)("div",{className:"space-y-3",children:e.map(((e,t)=>(0,va.jsx)(pt,{to:e.route,className:"block w-full ".concat(e.color," text-white rounded-lg p-4 transition-all duration-200 hover:transform hover:-translate-y-1 hover:shadow-lg"),children:(0,va.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,va.jsx)("i",{className:"".concat(e.icon," text-xl")}),(0,va.jsxs)("div",{children:[(0,va.jsx)("h4",{className:"font-medium",children:e.title}),(0,va.jsx)("p",{className:"text-sm opacity-90",children:e.description})]})]})},t)))})})]})},Pa=e=>{var t,n,a;let{availability:o,onSubmit:i,onClose:s}=e;const[l,c]=(0,r.useState)("conversation_practice"),[u,d]=(0,r.useState)(""),[h,f]=(0,r.useState)(!1),p=e=>{const[t,n]=e.split(":"),r=new Date;return r.setHours(parseInt(t),parseInt(n)),r.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})},m=(null===(t=o.tutor)||void 0===t?void 0:t.name)||"Tutor",g=(null===(n=o.tutor)||void 0===n?void 0:n.email)||"",v=(null===(a=o.tutor)||void 0===a?void 0:a.profile_picture)||"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(m),"&background=3b82f6&color=fff"),y=[{value:"conversation_practice",label:"Conversation Practice",price:500},{value:"grammar_lesson",label:"Grammar Lesson",price:600},{value:"pronunciation",label:"Pronunciation",price:550},{value:"trial",label:"Trial Lesson",price:300}],b=y.find((e=>e.value===l));return(0,va.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&s()},children:(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,va.jsx)("div",{className:"p-6 border-b",children:(0,va.jsxs)("div",{className:"flex justify-between items-center",children:[(0,va.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Book a Lesson"}),(0,va.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,va.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,va.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,va.jsxs)("div",{className:"p-6",children:[(0,va.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,va.jsx)("img",{src:v,alt:m,className:"w-16 h-16 rounded-full object-cover"}),(0,va.jsxs)("div",{children:[(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:m}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:g})]})]}),(0,va.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,va.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Selected Time Slot"}),(0,va.jsxs)("p",{className:"text-blue-800",children:[(0,va.jsx)("i",{className:"fas fa-calendar mr-2"}),(w=o.lesson_date,new Date(w).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}))]}),(0,va.jsxs)("p",{className:"text-blue-800",children:[(0,va.jsx)("i",{className:"fas fa-clock mr-2"}),p(o.start_time)," - ",p(o.end_time)]})]}),(0,va.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),f(!0);try{await i(l,u)}catch(t){console.error("Booking error:",t)}finally{f(!1)}},children:[(0,va.jsxs)("div",{className:"mb-6",children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Lesson Type"}),(0,va.jsx)("div",{className:"space-y-2",children:y.map((e=>(0,va.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,va.jsx)("input",{type:"radio",name:"lessonType",value:e.value,checked:l===e.value,onChange:e=>c(e.target.value),className:"text-blue-600 focus:ring-blue-500"}),(0,va.jsx)("span",{className:"flex-1 text-sm text-gray-700",children:e.label}),(0,va.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["\u20b9",e.price]})]},e.value)))})]}),(0,va.jsxs)("div",{className:"mb-6",children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes (Optional)"}),(0,va.jsx)("textarea",{value:u,onChange:e=>d(e.target.value),rows:3,className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Any specific topics or goals for this lesson?"})]}),(0,va.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,va.jsxs)("div",{className:"flex justify-between items-center",children:[(0,va.jsx)("span",{className:"font-medium text-gray-900",children:"Total Price:"}),(0,va.jsxs)("span",{className:"text-xl font-bold text-green-600",children:["\u20b9",(null===b||void 0===b?void 0:b.price)||500]})]})}),(0,va.jsx)("button",{type:"submit",disabled:h,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:h?"Booking...":"Book Lesson"})]})]})]})});var w},Oa=()=>{const[e,t]=(0,r.useState)(null),n=pe(),[a,o]=(0,r.useState)([]),[i,s]=(0,r.useState)([]),[l,c]=(0,r.useState)([]),[d,h]=(0,r.useState)(null),[f,p]=(0,r.useState)(!1),[m,g]=(0,r.useState)(!0),[v,y]=(0,r.useState)(0);(0,r.useEffect)((()=>{b()}),[]),(0,r.useEffect)((()=>{e&&(w(),j())}),[e]);const b=async()=>{const{data:{session:e}}=await ua.auth.getSession();e?t(e.user):n(da)},w=async()=>{g(!0);try{await Promise.all([k(),x(),S(),_()])}catch(e){console.error("Error loading dashboard data:",e),E("Error loading dashboard data","error")}finally{g(!1)}},k=async()=>{try{const{data:e,error:t}=await ua.from("students").select("*").eq("role","tutor");if(t)throw t;o(e||[])}catch(e){console.error("Error loading tutors:",e)}},x=async()=>{try{const e=(new Date).toISOString().split("T")[0],{data:t,error:n}=await ua.from("availability").select("\n          *,\n          tutor:tutor_id (\n            name,\n            email,\n            profile_picture\n          )\n        ").eq("is_booked",!1).gte("lesson_date",e).order("lesson_date",{ascending:!0}).order("start_time",{ascending:!0});if(n)throw n;s(t||[])}catch(e){console.error("Error loading availability:",e)}},S=async()=>{try{if(!e)return;console.log("\ud83d\udcda [STUDENT] Loading lessons for student:",e.id);const{data:t,error:n}=await ua.from("lessons").select("*").eq("student_id",e.id).order("lesson_date",{ascending:!0}).order("start_time",{ascending:!0});if(!n&&t){const e=t.map((e=>u(u({},e),{},{tutor_name:"Tutor",tutor_email:"<EMAIL>"})));c(e),console.log("\u2705 [STUDENT] Successfully loaded",e.length,"lessons")}else console.warn("\u26a0\ufe0f [STUDENT] Query failed:",null===n||void 0===n?void 0:n.message),c([])}catch(t){console.error("\u274c [STUDENT] Error loading lessons:",t),c([])}},_=async()=>{try{if(!e)return;const{data:t,error:n}=await ua.rpc("messaging_get_unread_count",{user_uuid:e.id});if(n)return void console.warn("Could not load unread message count:",n.message);y(t||0)}catch(t){console.warn("Error loading unread message count:",t)}},j=()=>{try{if(!e)return;ua.channel("unread_messages_".concat(e.id)).on("postgres_changes",{event:"*",schema:"public",table:"unread_messages",filter:"user_id=eq.".concat(e.id)},(e=>{console.log("\ud83d\udce8 [STUDENT] Unread count changed:",e),_()})).subscribe((e=>{"SUBSCRIBED"===e?console.log("\u2705 [STUDENT] Unread count subscription active"):"CHANNEL_ERROR"===e&&console.log("\u26a0\ufe0f [STUDENT] Unread count subscription failed")}));console.log("\u2705 [STUDENT] Real-time unread count subscription setup")}catch(t){console.warn("Error setting up unread count subscription:",t)}},E=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";const n=document.createElement("div");n.className="fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ".concat("success"===t?"bg-green-500 text-white":"error"===t?"bg-red-500 text-white":"bg-blue-500 text-white"),n.textContent=e,document.body.appendChild(n),setTimeout((()=>{n.remove()}),3e3)};return m?(0,va.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("div",{className:"loading-spinner"}),(0,va.jsx)("p",{className:"text-gray-600 mt-4",children:"Loading dashboard..."})]})}):(0,va.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,va.jsx)(ja,{user:e,unreadCount:v,onSignOut:async()=>{try{const{error:e}=await ua.auth.signOut();if(e)throw e;n(da)}catch(e){console.error("Logout error:",e),E("Failed to logout. Please try again.","error")}}}),(0,va.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,va.jsx)(Ea,{lessons:l,availability:i,tutors:a}),(0,va.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,va.jsx)("div",{className:"lg:col-span-2",children:(0,va.jsx)(Na,{tutors:a,availability:i,onBookingClick:e=>{h(e),p(!0)}})}),(0,va.jsxs)("div",{className:"space-y-6",children:[(0,va.jsx)(Ca,{lessons:l}),(0,va.jsx)(Ta,{})]})]})]}),f&&d&&(0,va.jsx)(Pa,{availability:d,onSubmit:async(t,n)=>{if(d&&e)try{const r="trial"===t?10:25,{data:a,error:o}=await ua.from("lessons").insert([{tutor_id:d.tutor_id,student_id:e.id,availability_id:d.id,lesson_date:d.lesson_date,start_time:d.start_time,duration:60,lesson_type:t,price:r,notes:n||null}]).select();if(o)throw o;console.log("\u2705 [BOOKING] Lesson booked successfully:",a),E("Lesson booked successfully!","success"),p(!1),h(null),await w()}catch(r){console.error("\u274c [BOOKING] Error booking lesson:",r),E("Error booking lesson: "+r.message,"error")}},onClose:()=>{p(!1),h(null)}})]})},Ra=()=>{const[e,t]=(0,r.useState)(null),[n,a]=(0,r.useState)(!0),o=pe();(0,r.useEffect)((()=>{i()}),[]);const i=async()=>{const{data:{session:e}}=await ua.auth.getSession();e?(t(e.user),a(!1)):o(da)};return n?(0,va.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("div",{className:"loading-spinner"}),(0,va.jsx)("p",{className:"text-gray-600 mt-4",children:"Loading messages..."})]})}):(0,va.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,va.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,va.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,va.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,va.jsx)("div",{className:"flex items-center",children:(0,va.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Messages"})}),(0,va.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,va.jsx)("button",{onClick:()=>o(ha),className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Back to Dashboard"}),(0,va.jsx)("button",{onClick:async()=>{try{const{error:e}=await ua.auth.signOut();if(e)throw e;o(da)}catch(e){console.error("Logout error:",e)}},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Sign Out"})]})]})})}),(0,va.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border p-8 text-center",children:[(0,va.jsxs)("div",{className:"mb-6",children:[(0,va.jsx)("i",{className:"fas fa-comments text-6xl text-gray-300 mb-4"}),(0,va.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Messages Coming Soon"}),(0,va.jsx)("p",{className:"text-gray-600 mb-6",children:"The messaging system is being converted to React. This page will include:"})]}),(0,va.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[(0,va.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,va.jsx)("i",{className:"fas fa-chat text-blue-600 text-2xl mb-2"}),(0,va.jsx)("h3",{className:"font-semibold text-gray-900",children:"Real-time Chat"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Live messaging with tutors"})]}),(0,va.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,va.jsx)("i",{className:"fas fa-bell text-green-600 text-2xl mb-2"}),(0,va.jsx)("h3",{className:"font-semibold text-gray-900",children:"Notifications"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Unread message alerts"})]}),(0,va.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,va.jsx)("i",{className:"fas fa-history text-purple-600 text-2xl mb-2"}),(0,va.jsx)("h3",{className:"font-semibold text-gray-900",children:"Chat History"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Previous conversations"})]})]}),(0,va.jsxs)("div",{className:"space-y-4",children:[(0,va.jsx)("p",{className:"text-gray-500",children:"For now, you can use the contact buttons on tutor profiles to start conversations."}),(0,va.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,va.jsx)("button",{onClick:()=>o(fa),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Find Tutors"}),(0,va.jsx)("button",{onClick:()=>o(ha),className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Back to Dashboard"})]})]})]})})]})},La=()=>{const[e,t]=(0,r.useState)(null),[n,a]=(0,r.useState)(!0),o=pe();(0,r.useEffect)((()=>{i()}),[]);const i=async()=>{const{data:{session:e}}=await ua.auth.getSession();e?(t(e.user),a(!1)):o(da)},s=()=>{var t,n;return e?(null===(t=e.user_metadata)||void 0===t?void 0:t.full_name)||(null===(n=e.email)||void 0===n?void 0:n.split("@")[0])||"Student":"Loading..."};return n?(0,va.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("div",{className:"loading-spinner"}),(0,va.jsx)("p",{className:"text-gray-600 mt-4",children:"Loading profile..."})]})}):(0,va.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,va.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,va.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,va.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,va.jsx)("div",{className:"flex items-center",children:(0,va.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Profile"})}),(0,va.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,va.jsx)("button",{onClick:()=>o(ha),className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Back to Dashboard"}),(0,va.jsx)("button",{onClick:async()=>{try{const{error:e}=await ua.auth.signOut();if(e)throw e;o(da)}catch(e){console.error("Logout error:",e)}},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Sign Out"})]})]})})}),(0,va.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border overflow-hidden",children:[(0,va.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8",children:(0,va.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,va.jsx)("img",{src:(()=>{var t;if(!e)return"";const n=s();return(null===(t=e.user_metadata)||void 0===t?void 0:t.avatar_url)||"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(n),"&background=3b82f6&color=fff")})(),alt:"Profile",className:"w-20 h-20 rounded-full border-4 border-white"}),(0,va.jsxs)("div",{children:[(0,va.jsx)("h2",{className:"text-2xl font-bold text-white",children:s()}),(0,va.jsx)("p",{className:"text-blue-100",children:null===e||void 0===e?void 0:e.email}),(0,va.jsx)("span",{className:"inline-block bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm mt-2",children:"Student"})]})]})}),(0,va.jsxs)("div",{className:"p-6",children:[(0,va.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,va.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Account Information"}),(0,va.jsxs)("div",{className:"space-y-3",children:[(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,va.jsx)("p",{className:"text-gray-900",children:s()})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,va.jsx)("p",{className:"text-gray-900",children:null===e||void 0===e?void 0:e.email})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,va.jsx)("p",{className:"text-gray-900",children:"Student"})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Member Since"}),(0,va.jsx)("p",{className:"text-gray-900",children:new Date(null===e||void 0===e?void 0:e.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,va.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Learning Preferences"}),(0,va.jsxs)("div",{className:"space-y-3",children:[(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Preferred Languages"}),(0,va.jsx)("p",{className:"text-gray-500",children:"Not set"})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Learning Goals"}),(0,va.jsx)("p",{className:"text-gray-500",children:"Not set"})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Skill Level"}),(0,va.jsx)("p",{className:"text-gray-500",children:"Not set"})]}),(0,va.jsxs)("div",{children:[(0,va.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Preferred Schedule"}),(0,va.jsx)("p",{className:"text-gray-500",children:"Not set"})]})]})]})]}),(0,va.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6 text-center",children:[(0,va.jsx)("i",{className:"fas fa-tools text-blue-600 text-3xl mb-3"}),(0,va.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Profile Editing Coming Soon"}),(0,va.jsx)("p",{className:"text-gray-600 mb-4",children:"We're working on adding profile editing features. Soon you'll be able to:"}),(0,va.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("i",{className:"fas fa-edit text-blue-600 text-xl mb-2"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Edit personal information"})]}),(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("i",{className:"fas fa-language text-blue-600 text-xl mb-2"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Set learning preferences"})]}),(0,va.jsxs)("div",{className:"text-center",children:[(0,va.jsx)("i",{className:"fas fa-camera text-blue-600 text-xl mb-2"}),(0,va.jsx)("p",{className:"text-sm text-gray-600",children:"Upload profile picture"})]})]}),(0,va.jsx)("button",{onClick:()=>o(ha),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Back to Dashboard"})]})]})]})})]})},Aa=()=>(0,va.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,va.jsx)("div",{className:"max-w-md w-full text-center",children:(0,va.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border p-8",children:[(0,va.jsxs)("div",{className:"mb-6",children:[(0,va.jsx)("i",{className:"fas fa-exclamation-triangle text-6xl text-yellow-500 mb-4"}),(0,va.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"404"}),(0,va.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-4",children:"Page Not Found"}),(0,va.jsx)("p",{className:"text-gray-600 mb-6",children:"The page you're looking for doesn't exist in the React version of the app."})]}),(0,va.jsxs)("div",{className:"space-y-3",children:[(0,va.jsx)(pt,{to:da,className:"block w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Go to Home"}),(0,va.jsx)(pt,{to:ha,className:"block w-full bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Go to Dashboard"}),(0,va.jsx)(pt,{to:fa,className:"block w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Find Tutors"})]}),(0,va.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,va.jsxs)("p",{className:"text-sm text-gray-500",children:["Looking for the original HTML version?"," ",(0,va.jsx)("a",{href:"https://shyamsyangtan.com",className:"text-blue-600 hover:text-blue-700 font-medium",target:"_blank",rel:"noopener noreferrer",children:"Visit the main site"})]})})]})})});const Ia=function(){return(0,va.jsx)("div",{className:"App",children:(0,va.jsx)(ht,{basename:"/my-tutor-app/react-version",children:(0,va.jsxs)(Oe,{children:[(0,va.jsx)(Te,{path:"/",element:(0,va.jsx)(wa,{})}),(0,va.jsx)(Te,{path:"/marketplace",element:(0,va.jsx)(_a,{})}),(0,va.jsx)(Te,{path:"/dashboard",element:(0,va.jsx)(Oa,{})}),(0,va.jsx)(Te,{path:"/home",element:(0,va.jsx)(Oa,{})}),(0,va.jsx)(Te,{path:"/messages",element:(0,va.jsx)(Ra,{})}),(0,va.jsx)(Te,{path:"/profile",element:(0,va.jsx)(La,{})}),(0,va.jsx)(Te,{path:"*",element:(0,va.jsx)(Aa,{})})]})})})},za=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:i}=t;n(e),r(e),a(e),o(e),i(e)}))};a.createRoot(document.getElementById("root")).render((0,va.jsx)(r.StrictMode,{children:(0,va.jsx)(Ia,{})})),za()})();
//# sourceMappingURL=main.78259ba2.js.map