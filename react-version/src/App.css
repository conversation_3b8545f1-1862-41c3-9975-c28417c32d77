/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties - Teal Professional Theme */
:root {
    /* Teal Professional Color Palette */
    --primary-color: #00C2B3;
    --primary-hover: #00A89B;
    --secondary-color: #007B83;
    --accent-color: #00A59C;
    --text-primary: #1E1E1E;
    --text-secondary: #5A5A5A;
    --text-light: #9E9E9E;
    --background: #FCFCFD;
    --background-light: #FFFFFF;
    --surface: #FFFFFF;
    --border-color: #E0E0E0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -4px rgba(0, 0, 0, 0.05);
    --gradient-primary: linear-gradient(135deg, #00C2B3 0%, #00A89B 100%);
    --gradient-secondary: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);
    --gradient-background: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);

    /* Special Colors */
    --star-rating: #FFC107;
    --availability: #38B000;
    --verified: #38B000;
    --accent-text: #00A59C;
    --success-light: #C7F4E5;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
    min-height: 100vh;
    width: 100%;
    max-width: 100vw;
}

/* Container System */
.responsive-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    box-sizing: border-box;
}

/* Global container fixes for mobile */
.container, .main-container, .page-container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .responsive-container {
        padding: 0 1.5rem;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    padding: 0.75rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.nav-logo {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.nav-logo:hover {
    color: var(--primary-hover);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.nav-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Profile Dropdown Styles */
.profile-dropdown {
    position: relative;
}

.profile-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.profile-btn:hover {
    background: var(--background);
    color: var(--text-primary);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-name {
    font-weight: 500;
    color: var(--text-primary);
}

.profile-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    min-width: 200px;
    z-index: 1000;
    margin-top: 0.5rem;
}

.profile-info {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dropdown-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown-user-info {
    display: flex;
    flex-direction: column;
}

.dropdown-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.dropdown-email {
    color: var(--text-light);
    font-size: 0.75rem;
}

.dropdown-divider {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 0;
}

.profile-dropdown-content a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
}

.profile-dropdown-content a:hover {
    background: var(--background);
    color: var(--text-primary);
}

.profile-dropdown-content a svg {
    width: 16px;
    height: 16px;
}

.logout-link {
    color: #dc2626 !important;
}

.logout-link:hover {
    background: #fef2f2 !important;
    color: #dc2626 !important;
}

.logout-link svg {
    color: #dc2626 !important;
}

/* Loading Screen */
.loading-screen {
    min-height: 100vh;
    background: var(--background);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
}

/* Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: 1px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Hero Section with Light Grey Background */
.hero {
    padding: 8rem 0 4rem;
    background: var(--gradient-background);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-features {
    list-style: none;
    margin-bottom: 3rem;
}

.hero-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.feature-icon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.cta-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    min-width: 250px;
    justify-content: center;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.google-icon {
    background: white;
    border-radius: 6px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hero Illustration */
.hero-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.illustration-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    width: 300px;
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.illustration-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
}

.tutor-info {
    flex: 1;
}

/* Removed conflicting .tutor-name skeleton rule that was causing black bars */

.tutor-lang {
    height: 8px;
    background: var(--text-light);
    border-radius: 4px;
    width: 60%;
}

.lesson-preview {
    height: 120px;
    background: var(--gradient-secondary);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.lesson-preview::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 4px;
    opacity: 0.3;
}

.lesson-preview::after {
    content: '';
    position: absolute;
    top: 40px;
    left: 20px;
    right: 60px;
    height: 6px;
    background: var(--text-light);
    border-radius: 3px;
    opacity: 0.5;
}

/* Languages Section with Cream Background */
.languages {
    padding: 4rem 0;
    background: var(--surface);
}

.languages-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.languages-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.language-pill {
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.language-pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.language-pill:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    background: var(--background-light);
}

.language-pill:hover::before {
    opacity: 1;
}

.language-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.tutor-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Grid System */
.responsive-grid {
    display: grid;
    gap: 1.5rem;
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

.grid-mobile-2 {
    grid-template-columns: repeat(2, 1fr);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background 0.2s ease;
    color: var(--text-secondary);
}

.modal-close:hover {
    background: var(--background);
}

.modal-body {
    padding: 1.5rem;
}

.modal-subtitle {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    text-align: center;
}

.google-login-btn {
    width: 100%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.google-login-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.google-icon-wrapper {
    background: white;
    border-radius: 4px;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-divider {
    text-align: center;
    margin: 1rem 0;
    position: relative;
    color: var(--text-light);
}

.modal-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.modal-divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.alternative-options {
    text-align: center;
}

.coming-soon {
    color: var(--text-light);
    font-size: 0.875rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--background);
}

.terms-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.4;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-dropdown {
        display: none;
    }
}

/* Mobile Navigation Styles */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.mobile-menu-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.hamburger-line {
    width: 24px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.hamburger-line.open:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
    opacity: 0;
}

.hamburger-line.open:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1998;
    backdrop-filter: blur(2px);
}

/* Mobile Menu Drawer */
.mobile-menu-drawer {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    background: white;
    z-index: 1999;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
    overflow-y: auto;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--background);
}

.mobile-menu-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mobile-menu-close {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.mobile-menu-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
}

.mobile-menu-content {
    padding: 1rem 0;
}

.mobile-user-info {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.mobile-user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    border: 2px solid var(--border-color);
}

.mobile-user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.mobile-menu-items {
    display: flex;
    flex-direction: column;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    text-align: left;
    font-size: 1rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 56px;
    position: relative;
}

.mobile-menu-item:hover {
    background-color: var(--background);
    color: var(--primary-color);
}

.mobile-menu-item svg {
    margin-right: 12px;
    flex-shrink: 0;
}

.mobile-menu-item.login-item {
    background: var(--primary-color);
    color: white;
    margin: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
}

.mobile-menu-item.login-item:hover {
    background: var(--primary-hover);
    color: white;
}

.mobile-menu-item.mode-toggle {
    color: var(--primary-color);
    font-weight: 500;
}

.mobile-menu-item.sign-out {
    color: #ef4444;
}

.mobile-menu-item.sign-out:hover {
    background-color: #fef2f2;
    color: #dc2626;
}

.mobile-menu-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 1.5rem;
}

.mobile-message-badge {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

/* Desktop Navigation Enhancements */
.desktop-nav {
    display: flex;
}

.nav-messages {
    position: relative;
}

.message-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.profile-dropdown {
    position: relative;
}

.profile-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.profile-trigger:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

.profile-name {
    font-weight: 500;
    color: var(--text-primary);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-arrow {
    color: var(--text-secondary);
    transition: transform 0.2s ease;
}

.profile-dropdown.open .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.dropdown-menu a:hover {
    background-color: var(--background);
    color: var(--primary-color);
}

.dropdown-menu a svg {
    margin-right: 8px;
    flex-shrink: 0;
}

.dropdown-menu .mode-toggle {
    color: var(--primary-color);
    font-weight: 500;
}

.dropdown-menu .sign-out {
    color: #ef4444;
}

.dropdown-menu .sign-out:hover {
    background-color: #fef2f2;
    color: #dc2626;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0;
    border: none;
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .desktop-nav {
        display: none;
    }

    .nav-container {
        padding: 0 1rem;
        position: relative;
    }

    .nav-logo {
        font-size: 1.25rem;
    }

    /* Mobile Tutor Marketplace Styles */
    .tutor-marketplace {
        padding-top: 80px;
    }

    .marketplace-header {
        padding: 1rem;
        text-align: center;
    }

    .marketplace-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .marketplace-subtitle {
        font-size: 1rem;
    }

    .marketplace-container {
        padding: 1rem;
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }

    .tutors-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 0;
        width: 100%;
    }

    .tutors-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        width: 100%;
        padding: 0;
    }

    /* Mobile Tutor Card - Rectangular Layout */
    .tutor-card-container {
        flex-direction: row;
        gap: 0;
        width: 100%;
        min-height: 120px;
        max-width: 100%;
        overflow: hidden;
    }

    .tutor-card {
        flex-direction: row;
        padding: 1rem;
        width: 100%;
        min-height: 120px;
        display: flex;
        align-items: stretch;
    }

    .tutor-left-section {
        min-width: auto;
        width: 100%;
        margin-bottom: 0;
        display: flex;
        flex-direction: row;
        gap: 1rem;
        align-items: flex-start;
    }

    .tutor-avatar-container {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
    }

    .tutor-avatar {
        width: 60px;
        height: 60px;
    }

    .tutor-info-section {
        flex: 1;
        min-width: 0;
    }

    .tutor-name-row {
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 0.25rem;
    }

    .tutor-name {
        font-size: 1rem;
        margin: 0;
    }

    .tutor-subtitle {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .tutor-stats-row {
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .stat-item {
        font-size: 0.75rem;
    }

    .language-badges {
        margin-bottom: 0.5rem;
    }

    .language-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .tutor-tags {
        margin-bottom: 0.5rem;
    }

    .tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .tutor-pricing {
        margin-top: auto;
    }

    .price-amount {
        font-size: 1.25rem;
    }

    .tutor-right-section {
        width: auto;
        align-items: center;
        justify-content: center;
        padding-left: 1rem;
        flex-shrink: 0;
    }

    .tutor-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: auto;
        min-width: 100px;
    }

    .tutor-actions .action-btn {
        width: 100%;
        min-height: 44px;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    /* Mobile Video Card Adjustments - Hide video space on mobile */
    .tutor-video-space {
        display: none;
    }

    /* Disable video hover on mobile */
    .tutor-card:hover .tutor-video-card {
        opacity: 0;
        visibility: hidden;
    }

    /* Mobile Search Filters */
    .search-filters {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .filter-group {
        width: 100%;
    }

    .filter-group select,
    .filter-group input {
        width: 100%;
        min-height: 44px;
        font-size: 1rem;
    }

    /* Mobile Dashboard Styles */
    .dashboard-main {
        padding: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .welcome-section h1 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    /* Mobile Profile Styles */
    .tutor-profile-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .tutor-header-info {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .tutor-avatar-section {
        align-self: center;
    }

    .tutor-actions-sidebar {
        position: static;
        width: 100%;
        padding: 1rem;
    }

    .action-button {
        width: 100%;
        min-height: 44px;
        margin-bottom: 0.5rem;
    }

    /* Mobile Form Styles */
    .form-container {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        min-height: 44px;
        font-size: 1rem;
        padding: 12px;
    }

    .form-button {
        width: 100%;
        min-height: 44px;
        font-size: 1rem;
    }

    /* Mobile Modal Styles */
    .modal-content {
        width: 95%;
        max-width: none;
        margin: 1rem;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-footer .btn {
        width: 100%;
        min-height: 44px;
    }

    .hero {
        padding: 4rem 0 2rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
        padding: 0 1rem;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    .hero-features {
        margin-bottom: 1.5rem;
        gap: 1rem;
    }

    .hero-features li {
        justify-content: center;
        font-size: 0.9rem;
        padding: 0.5rem;
    }

    .feature-icon {
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }

    .cta-button {
        width: 100%;
        max-width: 320px;
        margin: 0 auto;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
    }

    .languages-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .languages-title {
        font-size: 1.75rem;
        margin-bottom: 1.5rem;
    }

    .language-pill {
        padding: 0.75rem;
        text-align: center;
    }

    .language-name {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .tutor-count {
        font-size: 0.75rem;
    }

    .hero-image {
        order: -1;
        margin-bottom: 1rem;
    }

    .hero-illustration {
        max-width: 280px;
        margin: 0 auto;
    }

    .btn-full-mobile {
        width: 100%;
        justify-content: center;
    }
}

/* Tablet Responsive Design */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-container {
        gap: 2rem;
        padding: 0 1.5rem;
    }

    .hero-title {
        font-size: 2.75rem;
    }

    .languages-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .cta-button {
        max-width: 280px;
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    .hero {
        padding: 3rem 0 1.5rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-features {
        gap: 0.75rem;
    }
}

/* TUTOR MARKETPLACE STYLES */

/* Marketplace Layout */
.tutor-marketplace {
    max-width: 1200px;
    margin: 0 auto;
    padding: 80px 1rem 2rem 1rem; /* Add 80px top padding for fixed header */
    background: var(--background);
    min-height: 100vh;
}

.marketplace-header {
    margin-bottom: 2rem;
}

.marketplace-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.marketplace-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
}

.tutor-count {
    color: var(--primary-color);
    font-weight: 600;
}

/* Search Filters */
.search-filters {
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.search-group {
    flex: 1;
    min-width: 200px;
}

.clear-group {
    justify-content: end;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.filter-select,
.filter-input {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background: var(--surface);
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 194, 179, 0.1);
}

.clear-filters-btn {
    background: var(--background);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-filters-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* Results Section */
.marketplace-results {
    margin-top: 2rem;
}

.tutors-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 1024px) {
    .tutors-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* No Results */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
}

.no-results-icon {
    width: 3rem;
    height: 3rem;
    color: var(--text-light);
    margin: 0 auto 1rem;
}

.no-results-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.no-results-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Loading Spinner */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    min-height: 50vh;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: var(--text-secondary);
    margin-top: 1rem;
    font-size: 0.875rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* TUTOR CARD STYLES - FIXED WIDTH LAYOUT WITH RESERVED SPACE */

/* Container for main card + video space - Fixed proportions */
.tutor-card-container {
    display: flex;
    align-items: stretch;
    gap: 20px;
    position: relative;
    width: 100%;
    min-height: 131px;
}

/* Main Tutor Card - Fixed 70% width */
.tutor-card {
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    width: 70%;
    flex-shrink: 0;
}

.tutor-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

/* Main Card Content Layout */
.tutor-card-content {
    display: flex;
    min-height: 99px;
    width: 100%;
    padding: 16px;
    gap: 16px;
}

/* Left Section: Avatar & Info */
.tutor-left-section {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    flex: 1;
}

.tutor-info-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

/* Right Section: Action Buttons */
.tutor-right-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    min-width: 200px;
}

.tutor-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
}

/* Reserved Video Space - Fixed 30% width (always present) */
.tutor-video-space {
    width: 30%;
    flex-shrink: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* Video Card - Only visibility changes on hover */
.tutor-video-card {
    width: 100%;
    aspect-ratio: 16/9; /* Standard YouTube aspect ratio */
    max-height: 216px;
    min-height: 180px;
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: none !important;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

.tutor-video-card.visible {
    opacity: 1;
    visibility: visible;
}

/* YouTube Thumbnail Container Enhancements */
.youtube-thumbnail-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 216px;
    border-radius: 8px;
    overflow: hidden;
    background: #000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.youtube-thumbnail-container:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.youtube-thumbnail-container:hover .youtube-play-overlay {
    background-color: rgba(255, 0, 0, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.youtube-play-overlay {
    transition: all 0.3s ease;
}

.youtube-play-overlay:hover {
    background-color: rgba(255, 0, 0, 1) !important;
}

/* Video click overlay for YouTube videos */
.video-click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-click-overlay .play-button,
.video-click-overlay .pause-button {
    transition: all 0.3s ease;
}

.video-click-overlay .play-button:hover,
.video-click-overlay .pause-button:hover {
    transform: scale(1.1);
}

.tutor-video-card .video-container {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 8px;
    overflow: hidden;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

.tutor-video-card .video-thumbnail {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    cursor: pointer;
    background: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

.tutor-video-card .video-poster,
.tutor-video-card img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 8px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    display: block !important;
    border: none !important;
    outline: none !important;
    max-width: none !important;
    max-height: none !important;
    min-width: 100% !important;
    min-height: 100% !important;
}

.video-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    background: #f0f0f0;
    box-shadow: var(--shadow-sm);
}

.video-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.view-schedule-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.view-schedule-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

/* Avatar & Basic Info Row */
.tutor-left-info {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.tutor-avatar-container {
    flex-shrink: 0;
}

.tutor-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.tutor-basic-info {
    flex: 1;
    min-width: 0;
}

.tutor-name-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    flex-wrap: wrap;
}

.tutor-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.country-flag {
    font-size: 16px;
    flex-shrink: 0;
}

.professional-badge {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.tutor-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.language-badges {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.language-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.language-badge.primary {
    background: var(--primary-color);
    color: white;
}

.language-badge.secondary {
    background: #f0f0f0;
    color: #666;
}

/* Stats & Details Section */
.tutor-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.tutor-stats-row {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--text-secondary);
}

.stat-item.rating {
    color: var(--text-primary);
}

.rating-stars {
    color: #ffc107;
    font-size: 16px;
}

.rating-value {
    font-weight: 600;
    color: var(--text-primary);
}

.stat-icon {
    width: 16px;
    height: 16px;
    color: var(--text-secondary);
}

.tutor-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.tag-1 {
    background: #e3f2fd;
    color: #1976d2;
}

.tag-2 {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* Bottom Section: Price & Actions */
.tutor-bottom-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

/* Pricing in Left Section */
.tutor-pricing {
    text-align: left;
    margin-top: 8px;
}

.price-amount {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.price-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
}

/* Action Buttons in Right Section */
.tutor-actions .action-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    width: 100%;
}

.tutor-actions .contact-btn {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.tutor-actions .contact-btn:hover {
    background: var(--primary-color);
    color: white;
}

.tutor-actions .book-btn {
    background: var(--primary-color);
    color: white;
}

.tutor-actions .book-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.tutor-actions .view-schedule-btn {
    background: #f0f0f0;
    color: #666;
    font-size: 12px;
    padding: 8px 16px;
}

.tutor-actions .view-schedule-btn:hover {
    background: #e0e0e0;
    color: #333;
}

/* Enhanced Video Preview Styles */
.video-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
}

/* Video Play Overlay */
.video-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    z-index: 10;
    backdrop-filter: blur(2px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.video-play-overlay:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
    width: 20px;
    height: 20px;
    color: white;
    margin-left: 2px;
}

/* Video Loading State */
.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.loading-text {
    font-size: 12px;
    margin: 0;
}

/* Video Error State */
.video-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-align: center;
}

.error-icon {
    width: 24px;
    height: 24px;
    color: #ef4444;
}

.error-text {
    font-size: 12px;
    margin: 0;
}

/* Video Placeholder */
.video-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-light);
    border-radius: 8px;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.video-icon {
    width: 32px;
    height: 32px;
}

.placeholder-text {
    font-size: 12px;
    margin: 0;
}

.video-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 1;
}

.tutor-card.hovered .video-play-overlay {
    opacity: 0;
}

.video-thumbnail:hover .video-play-overlay {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
    width: 16px;
    height: 16px;
    color: white;
    margin-left: 2px;
}

.video-action-btn {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    box-sizing: border-box;
}

.video-action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: cover;
    box-sizing: border-box;
}

.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* NEW HORIZONTAL LAYOUT STYLES */

/* Left Section: Avatar & Basic Info */
.tutor-left-section {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    flex-shrink: 0;
    min-width: 280px;
}

.tutor-left-section .tutor-avatar-container {
    width: 48px;
    height: 48px;
    flex-shrink: 0;
}

.tutor-left-section .tutor-avatar {
    width: 100%;
    height: 100%;
}

.tutor-left-section .video-indicator {
    width: 20px;
    height: 20px;
    bottom: -2px;
    right: -2px;
    background: var(--primary-color);
    border: 2px solid white;
    transition: all 0.3s ease;
}

.tutor-left-section .video-indicator.playing {
    background: var(--success-color);
    transform: scale(1.1);
}

.tutor-left-section .video-icon {
    width: 10px;
    height: 10px;
}

.tutor-basic-info {
    flex: 1;
    min-width: 0;
}

.tutor-name-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.tutor-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.country-flag {
    font-size: 1rem;
}

.tutor-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0 0 0.75rem 0;
    line-height: 1.3;
}

/* Language badges for horizontal layout */
.tutor-left-section .language-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0;
}

.tutor-left-section .language-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.tutor-left-section .language-badge.primary {
    background: var(--primary-color);
    color: white;
}

.tutor-left-section .language-badge.secondary {
    background: var(--success-light);
    color: var(--success-dark);
    border-color: var(--success-color);
}

/* Center Section: Stats & Tags */
.tutor-center-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0.5rem;
    min-width: 200px;
}

.tutor-stats-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.25rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.stat-item.rating {
    color: var(--warning-color);
}

.stat-icon {
    width: 14px;
    height: 14px;
}

.rating-stars {
    color: #FFC107;
    font-size: 0.8rem;
}

.rating-value {
    font-weight: 500;
    color: var(--text-primary);
}

/* Right Section: Price & Actions */
.tutor-right-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    flex-shrink: 0;
    min-width: 180px;
    gap: 1rem;
}

.tutor-pricing {
    text-align: right;
}

.price-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.price-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.tutor-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-btn {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.contact-btn:hover {
    background: var(--primary-color);
    color: white;
}

.book-btn {
    background: var(--primary-color);
    color: white;
}

.book-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.profile-btn {
    background: var(--primary-color);
    color: white;
}

.profile-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Remove duplicate video section - using side-by-side layout instead */

.preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--surface);
    gap: 0.5rem;
}

.video-loading .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* List Layout for Side-by-Side Cards */
.tutors-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 2rem;
}

/* Responsive adjustments for side-by-side cards */
@media (min-width: 768px) {
    .tutors-list {
        gap: 20px;
    }

    .tutor-card {
        min-height: 131px;
    }
}

/* Responsive Design for Fixed Width Layout */
@media (max-width: 1023px) {
    .tutor-video-space {
        display: none;
    }

    .tutor-card {
        width: 100%;
    }

    .tutor-card-container {
        gap: 0;
    }

    /* Show video indicator on mobile for tutors with videos */
    .tutor-avatar-container {
        position: relative;
    }

    .tutor-card[data-has-video="true"] .tutor-avatar-container::after {
        content: '🎬';
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 20px;
        height: 20px;
        background: var(--primary-color);
        border-radius: 50%;
        border: 2px solid white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

@media (max-width: 768px) {
    /* Override for rectangular mobile layout */
    .tutor-card-content {
        flex-direction: row;
        gap: 16px;
        align-items: stretch;
    }

    .tutor-left-section {
        flex-direction: row;
        align-items: flex-start;
        text-align: left;
        gap: 12px;
        flex: 1;
    }

    .tutor-right-section {
        align-items: center;
        min-width: auto;
        flex-shrink: 0;
        justify-content: center;
    }

    .tutor-info-section {
        align-items: flex-start;
        flex: 1;
        min-width: 0;
    }

    .tutor-stats-row {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .language-badges {
        justify-content: flex-start;
    }

    .tutor-pricing {
        text-align: left;
    }

    .tutor-actions {
        width: auto;
        max-width: 120px;
        flex-direction: column;
    }
}

/* Content Section */
.tutor-content {
    flex: 1;
    min-width: 0;
}

.tutor-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 0.75rem;
}

.tutor-info {
    flex: 1;
    min-width: 0;
}

.tutor-name-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.tutor-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.country-flag {
    font-size: 1.125rem;
}

.professional-badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    background: #4A5568;
    color: white;
    font-weight: 500;
}

.tutor-headline {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tutor-pricing {
    text-align: right;
    flex-shrink: 0;
}

.price-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.price-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Language Badges */
.language-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.language-badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    color: white;
    font-weight: 500;
}

.language-badge.primary {
    background: var(--primary-color);
}

.language-badge.secondary-1 {
    background: #6B7280;
}

.language-badge.secondary-2 {
    background: #4A5568;
}

.language-badge.more {
    background: var(--background);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* Stats */
.tutor-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.rating-stars {
    color: #FFC107;
}

.rating-value {
    font-weight: 500;
    color: var(--text-primary);
}

.stat-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Tags */
.tutor-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.tag {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.tag.tag-1 {
    background: #E2E8F0;
    color: #4A5568;
}

.tag.tag-2 {
    background: #F1F5F9;
    color: #2D3748;
}

.tag.tag-more {
    background: var(--background);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* Action Buttons */
.tutor-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.contact-btn {
    background: var(--primary-color);
    color: white;
}

.contact-btn:hover {
    background: var(--primary-hover);
}

.book-btn {
    background: #059669;
    color: white;
}

.book-btn:hover {
    background: #047857;
}

.profile-btn {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.profile-btn:hover {
    background: var(--background);
    border-color: var(--text-light);
}

/* Mobile Responsive for Tutor Cards */
@media (max-width: 767px) {
    .tutor-card-layout {
        flex-direction: column;
        text-align: left;
    }

    .tutor-header {
        flex-direction: column;
        text-align: left;
        gap: 0.5rem;
    }

    .tutor-pricing {
        text-align: left;
    }

    .tutor-stats {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .language-badges,
    .tutor-tags {
        justify-content: flex-start;
    }

    .tutor-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        flex: none;
    }
}

/* Tablet Responsive */
@media (min-width: 768px) and (max-width: 1023px) {
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .clear-group {
        align-self: flex-end;
    }
}

/* STUDENT DASHBOARD STYLES */

/* Dashboard Layout */
.min-h-screen {
    min-height: 100vh;
}

.bg-gray-50 {
    background-color: #F9FAFB;
}

.bg-white {
    background-color: #FFFFFF;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.border-b {
    border-bottom: 1px solid #E5E7EB;
}

.border {
    border: 1px solid #E5E7EB;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-full {
    border-radius: 9999px;
}

/* Grid System */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .md\\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\\:col-span-2 {
        grid-column: span 2 / span 2;
    }
}

/* Spacing */
.gap-6 {
    gap: 1.5rem;
}

.gap-8 {
    gap: 2rem;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Padding and Margin */
.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

/* Text Styles */
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

/* Colors */
.text-gray-900 {
    color: #111827;
}

.text-gray-700 {
    color: #374151;
}

.text-gray-600 {
    color: #4B5563;
}

.text-gray-500 {
    color: #6B7280;
}

.text-white {
    color: #FFFFFF;
}

.text-green-600 {
    color: #059669;
}

.text-red-500 {
    color: #EF4444;
}

/* Background Colors */
.bg-green-100 {
    background-color: #DCFCE7;
}

.bg-green-500 {
    background-color: #22C55E;
}

.bg-green-600 {
    background-color: #16A34A;
}

.bg-purple-100 {
    background-color: #F3E8FF;
}

.bg-purple-600 {
    background-color: #9333EA;
}

.bg-yellow-100 {
    background-color: #FEF3C7;
}

.bg-yellow-600 {
    background-color: #D97706;
}

.bg-blue-500 {
    background-color: #3B82F6;
}

.bg-blue-600 {
    background-color: #2563EB;
}

.bg-red-500 {
    background-color: #EF4444;
}

.bg-red-600 {
    background-color: #DC2626;
}

.bg-gray-50 {
    background-color: #F9FAFB;
}

/* Hover Effects */
.hover\\:bg-green-600:hover {
    background-color: #16A34A;
}

.hover\\:bg-blue-600:hover {
    background-color: #2563EB;
}

.hover\\:bg-red-600:hover {
    background-color: #DC2626;
}

.hover\\:text-green-600:hover {
    color: #059669;
}

/* Dashboard Specific Components */
.tutor-card {
    transition: all 0.3s ease;
}

.tutor-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.available-slot {
    transition: all 0.2s ease;
}

.available-slot:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.booked-lesson {
    transition: all 0.2s ease;
}

.booked-lesson:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

/* Dashboard Styles */
.dashboard-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.dashboard-main {
    padding: 2rem 0;
}

.welcome-section {
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.dashboard-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.view-all-btn {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.875rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.2s ease;
}

.view-all-btn:hover {
    color: var(--primary-hover);
}

/* Progress Card Styles */
.progress-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-avatar {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    object-fit: cover;
}

.progress-info {
    flex: 1;
}

.progress-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Lessons Card Styles */
.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.lesson-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background);
    border-radius: var(--border-radius);
}

.lesson-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lesson-date {
    text-align: center;
}

.date-text {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.time-text {
    color: var(--text-light);
    font-size: 0.875rem;
}

.lesson-details {
    display: flex;
    flex-direction: column;
}

.lesson-language {
    font-weight: 500;
    color: var(--text-primary);
}

.lesson-duration {
    color: var(--text-light);
    font-size: 0.875rem;
}

.lesson-join-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.no-lessons {
    text-align: center;
    padding: 2rem 0;
}

.no-lessons-text {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.book-lesson-btn {
    color: var(--primary-color);
    background: none;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
}

.book-lesson-btn:hover {
    color: var(--primary-hover);
}

/* Teachers Section */
.teachers-section {
    margin-top: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
}

.teachers-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .teachers-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .teachers-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.tutor-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tutor-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    object-fit: cover;
}

.tutor-info {
    flex: 1;
}

.tutor-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.tutor-language {
    color: var(--text-light);
    font-size: 0.875rem;
}

.tutor-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.tutor-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.star-icon {
    color: var(--star-rating);
}

.rating-value {
    font-weight: 500;
    color: var(--text-primary);
}

.tutor-rate {
    font-weight: 600;
    color: var(--text-primary);
}

.tutor-book-btn {
    width: 100%;
    padding: 0.75rem;
}

/* Become Tutor Info Page Styles */
.become-tutor-info-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.tutor-info-main {
    padding: 2rem 0;
}

.tutor-info-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: var(--primary-hover);
}

.header-section {
    text-align: center;
    margin-bottom: 3rem;
}

.header-section h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.header-section p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.requirement-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.requirement-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.requirement-card .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.requirement-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.requirement-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.action-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.info-section {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.info-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.info-section ul {
    list-style: none;
    padding: 0;
}

.info-section li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 1.5rem;
}

.info-section li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.start-application {
    text-align: center;
    background: var(--gradient-background);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.start-application h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.start-application p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.start-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Become Tutor Form Styles */
.become-tutor-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.tutor-main {
    padding: 2rem 0;
}

.become-tutor-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.become-tutor-container h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.become-tutor-container > p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 2rem;
}

.info-box {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.info-box h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.info-box p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.success-message, .error-message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    font-weight: 500;
}

.success-message {
    background: var(--success-light);
    color: var(--availability);
    border: 1px solid var(--availability);
}

.error-message {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #dc2626;
}

.tutor-form {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: var(--background-light);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 194, 179, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-group small {
    display: block;
    color: var(--text-light);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.submit-btn {
    width: 100%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Existing Application Status */
.existing-application {
    text-align: center;
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.existing-application h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.status-approved {
    background: var(--success-light);
    border: 1px solid var(--availability);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.status-approved h3 {
    color: var(--availability);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.status-pending {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.status-pending h3 {
    color: #92400e;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.application-details {
    background: var(--background);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: left;
    border: 1px solid var(--border-color);
}

.application-details h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
}

.application-details p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

/* Tutor Dashboard Styles */
.tutor-dashboard-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.tutor-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 1024px) {
    .tutor-dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Teacher Profile Card */
.teacher-profile-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.teacher-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.teacher-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--primary-color);
}

.teacher-info {
    flex: 1;
}

.teacher-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.teacher-id {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.teacher-status {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.approved {
    background: var(--success-light);
    color: var(--availability);
}

.language-badge {
    background: var(--background);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
}

.teacher-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-card {
    text-align: center;
    padding: 1rem;
    background: var(--background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.stat-card.action-required {
    cursor: pointer;
    background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
    border-color: #f59e0b;
}

.stat-card.action-required:hover {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-card.action-required .stat-number {
    color: #92400e;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card.action-required .stat-label {
    color: #92400e;
}

/* Quick Actions Card */
.quick-actions-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-actions-grid .action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--background);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
}

.quick-actions-grid .action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Earnings Card */
.earnings-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.earnings-total {
    text-align: center;
    margin-bottom: 2rem;
}

.total-amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.total-amount span {
    font-size: 1rem;
    font-weight: normal;
}

.total-label {
    color: var(--text-light);
    font-size: 0.875rem;
}

.earnings-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.earning-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.earning-item p {
    color: var(--text-light);
    font-size: 0.875rem;
}

.earnings-tip {
    background: var(--success-light);
    border: 1px solid var(--availability);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.tip-title {
    font-weight: 600;
    color: var(--availability);
    margin-bottom: 0.5rem;
}

.tip-text {
    color: var(--availability);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Activity Card */
.activity-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Error Screen */
.error-screen {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--background);
    padding: 2rem;
}

.error-screen h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.error-screen p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Tutor Profile Page Styles */
.tutor-profile-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.tutor-profile-main {
    padding: 2rem 0;
}

.tutor-profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.tutor-profile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 1024px) {
    .tutor-profile-grid {
        grid-template-columns: 2fr 1fr;
    }
}

.profile-main-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.profile-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Tutor Header Card */
.tutor-header-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.tutor-header-info {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.tutor-avatar-section {
    position: relative;
    flex-shrink: 0;
}

.tutor-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.online-status {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
    background: var(--availability);
    border-radius: 50%;
    border: 2px solid white;
}

.tutor-basic-info {
    flex: 1;
}

.tutor-name-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.tutor-profile-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.tutor-flag {
    font-size: 1.5rem;
}

.professional-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.tutor-languages {
    margin-bottom: 1rem;
}

.language-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.language-badge.primary {
    background: var(--primary-color);
    color: white;
}

.proficiency {
    font-size: 0.75rem;
    opacity: 0.9;
}

.tutor-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars {
    font-size: 1.25rem;
}

.rating-number {
    font-weight: 600;
    color: var(--text-primary);
}

.rating-count {
    color: var(--text-light);
    font-size: 0.875rem;
}

.tutor-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.tutor-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tutor-tag {
    background: var(--background);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
}

/* Tutor Tabs */
.tutor-tabs-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background: var(--background);
    overflow-x: auto;
}

.tab-button {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-light);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
}

.tab-button:hover {
    color: var(--text-primary);
    background: var(--background-light);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--surface);
}

.tab-content-area {
    padding: 2rem;
}

.tab-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.tab-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5rem 0 1rem 0;
}

.tab-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.interests-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.interest-tag {
    background: var(--background);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
}

/* Pricing Card */
.pricing-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    text-align: center;
}

.price-display {
    margin-bottom: 2rem;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.price-period {
    color: var(--text-light);
    font-size: 1rem;
    margin-left: 0.5rem;
}

.booking-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.book-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.book-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.contact-btn {
    background: var(--background);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.contact-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Video Card */
.video-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.video-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.video-placeholder {
    background: var(--background);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    border: 1px solid var(--border-color);
}

/* Reviews Card */
.reviews-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.reviews-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.review-item {
    background: var(--background);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.review-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.reviewer-name {
    font-weight: 500;
    color: var(--text-primary);
}

.review-rating {
    font-size: 0.875rem;
}

.review-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.view-all-reviews {
    width: 100%;
    background: var(--background);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-all-reviews:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Messages Page Styles */
.messages-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.messages-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.unread-badge {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.messages-main {
    height: calc(100vh - 80px);
    overflow: hidden;
}

.messages-container {
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Old messages-layout removed - now using full-screen interface */

/* Removed old conflicting mobile CSS - now using full-screen interface */

/* Chat Sidebar */
.chat-sidebar {
    background: var(--background);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface);
}

.chat-sidebar-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.new-chat-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.chat-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.no-chats {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
    text-align: center;
}

.no-chats-icon {
    width: 64px;
    height: 64px;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.no-chats p {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.no-chats small {
    color: var(--text-light);
    font-size: 0.875rem;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.chat-item:hover {
    background: var(--background-light);
}

.chat-item.active {
    background: var(--primary-color);
    color: white;
}

.chat-item.active .chat-name,
.chat-item.active .last-message,
.chat-item.active .chat-time {
    color: white;
}

.chat-avatar-container {
    position: relative;
    flex-shrink: 0;
}

.chat-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--availability);
    border-radius: 50%;
    border: 2px solid white;
}

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.chat-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.chat-time {
    color: var(--text-light);
    font-size: 0.75rem;
}

.chat-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin: 0;
}

.unread-count {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

/* Chat Area */
.chat-area {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--surface);
}

.no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 2rem;
}

.no-chat-icon {
    width: 80px;
    height: 80px;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.no-chat-selected h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-chat-selected p {
    color: var(--text-secondary);
}

.chat-header-bar {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background);
}

.chat-participant {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.participant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.participant-info {
    display: flex;
    flex-direction: column;
}

.participant-name {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

.participant-status {
    font-size: 0.875rem;
    color: var(--text-light);
}

.participant-status.online {
    color: var(--availability);
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.chat-action-btn {
    background: var(--background);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chat-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Messages Area */
.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background);
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    max-width: 70%;
}

.message.sent {
    align-self: flex-end;
    justify-content: flex-end;
}

.message.received {
    align-self: flex-start;
    justify-content: flex-start;
}

.message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 18px;
    max-width: 100%;
    word-wrap: break-word;
}

.message.sent .message-bubble {
    background: var(--gradient-primary);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.received .message-bubble {
    background: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
}

.message-content {
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.75;
    display: block;
}

/* Message Input Area */
.message-input-area {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--surface);
}

.message-input-container {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 24px;
    padding: 0.5rem;
}

.attachment-btn {
    background: none;
    border: none;
    color: var(--text-light);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.attachment-btn:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

.message-input {
    flex: 1;
    border: none;
    outline: none;
    background: none;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    color: var(--text-primary);
    padding: 0.5rem;
    max-height: 120px;
    min-height: 24px;
}

.message-input::placeholder {
    color: var(--text-light);
}

.send-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--text-light);
    cursor: not-allowed;
    transform: none;
}

/* Full-Screen Chat Interface */
.conversations-list-view {
    height: 100%;
    background: var(--background);
    display: flex;
    flex-direction: column;
}

.fullscreen-chat-view {
    height: 100%;
    background: var(--surface);
    display: flex;
    flex-direction: column;
}

.fullscreen-chat-header {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--surface);
    border-bottom: 1px solid var(--border-color);
    gap: 1rem;
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    min-width: 44px;
    min-height: 44px;
}

.back-btn:hover {
    background: var(--background);
}

.nav-back-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    min-width: 44px;
    min-height: 44px;
}

.nav-back-btn:hover {
    background: var(--background);
}

.fullscreen-messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background);
}

.fullscreen-message-input-area {
    padding: 1rem;
    background: var(--surface);
    border-top: 1px solid var(--border-color);
}

/* Responsive Messages */
@media (max-width: 768px) {
    .messages-page {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    .messages-container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        height: calc(100vh - 80px);
    }

    .conversations-list-view,
    .fullscreen-chat-view {
        height: 100%;
        border-radius: 0;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    /* Fix navigation on mobile to prevent duplicate profile sections */
    .nav-container {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }

    .nav-content {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .nav-center {
        flex: 1;
        text-align: center;
        overflow: hidden;
    }

    .messages-title {
        font-size: 1.25rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .message {
        max-width: 85%;
    }

    .message-input-container {
        border-radius: 20px;
    }

    .chat-sidebar-header {
        padding: 1rem;
    }

    .chat-item {
        padding: 0.75rem 1rem;
    }

    .fullscreen-chat-header {
        padding: 1rem;
    }

    .back-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

@media (max-width: 480px) {
    .messages-container {
        padding: 0;
    }

    .message {
        max-width: 90%;
    }

    .message-bubble {
        padding: 0.5rem 0.75rem;
    }

    .message-input-area {
        padding: 0.75rem 1rem;
    }
}

/* Flexbox Utilities */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.flex-1 {
    flex: 1 1 0%;
}

/* FOOTER COMPONENT STYLES */
.site-footer {
    background: var(--text-primary);
    color: #ffffff;
    margin-top: 4rem; /* Add proper spacing from page content */
    position: relative;
    width: 100vw; /* Full viewport width */
    margin-left: calc(-50vw + 50%); /* Break out of container */
    clear: both; /* Ensure footer clears any floated content */
}

/* Add spacing before footer on specific pages */
.tutor-marketplace .marketplace-results,
.dashboard-page .main-content,
.messages-main,
.tutor-profile-main {
    margin-bottom: 2rem;
}

/* Footer container - consistent for all variants */
.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
    box-sizing: border-box;
}

/* Ensure footer spans full width - simpler approach */
.site-footer {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
}

/* Ensure full footer content is visible */
.site-footer.full .footer-content {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.site-footer.full .footer-section {
    display: flex !important;
    visibility: visible !important;
}

.site-footer.full .social-links {
    display: flex !important;
    visibility: visible !important;
}

.site-footer.full .footer-bottom {
    display: block !important;
    visibility: visible !important;
}

/* Hide minimal footer content in full footer */
.site-footer.full .footer-minimal-content {
    display: none !important;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 0 2rem;
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 3rem;
    }
}

.footer-section {
    display: flex;
    flex-direction: column;
}

.footer-brand {
    max-width: 350px;
}

.footer-logo {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-description {
    color: #b0b0b0;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.footer-social {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.social-label {
    color: #ffffff;
    font-weight: 500;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 194, 179, 0.3);
}

.footer-section-title {
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-link {
    color: #b0b0b0;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    padding: 0.25rem 0;
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
}

.footer-bottom-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    text-align: center;
}

@media (min-width: 768px) {
    .footer-bottom-content {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}

.copyright {
    color: #b0b0b0;
    font-size: 0.875rem;
    margin: 0;
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-link {
    color: #b0b0b0;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-bottom-link:hover {
    color: var(--primary-color);
}

/* Minimal Footer Styles */
.footer-minimal-content {
    padding: 1.5rem 0;
    text-align: center;
}

.site-footer.minimal .copyright {
    color: #b0b0b0;
    font-size: 0.875rem;
    margin: 0;
}

/* Mobile Footer Adjustments */
@media (max-width: 767px) {
    .site-footer {
        margin-top: 2rem; /* Reduced margin on mobile */
    }

    .footer-content {
        padding: 2rem 0 1.5rem;
        gap: 1.5rem;
    }

    .footer-brand {
        max-width: 100%;
    }

    .footer-logo {
        font-size: 1.5rem;
    }

    .social-links {
        justify-content: flex-start;
    }

    .footer-bottom-links {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-minimal-content {
        padding: 1rem 0;
    }
}

.flex-shrink-0 {
    flex-shrink: 0;
}

/* Positioning */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.top-4 {
    top: 1rem;
}

.right-4 {
    right: 1rem;
}

.top-0 {
    top: 0;
}

.left-0 {
    left: 0;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.-top-1 {
    top: -0.25rem;
}

.-right-1 {
    right: -0.25rem;
}

/* Sizing */
.w-6 {
    width: 1.5rem;
}

.w-8 {
    width: 2rem;
}

.w-10 {
    width: 2.5rem;
}

.w-16 {
    width: 4rem;
}

.w-full {
    width: 100%;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-8 {
    height: 2rem;
}

.h-10 {
    height: 2.5rem;
}

.h-16 {
    height: 4rem;
}

.max-w-7xl {
    max-width: 80rem;
}

.max-w-md {
    max-width: 28rem;
}

.max-h-96 {
    max-height: 24rem;
}

.max-h-\\[90vh\\] {
    max-height: 90vh;
}

/* Display */
.block {
    display: block;
}

.hidden {
    display: none;
}

/* Overflow */
.overflow-hidden {
    overflow: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
}

/* Z-index */
.z-50 {
    z-index: 50;
}

/* Transitions */
.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-200 {
    transition-duration: 200ms;
}

/* Transform */
.hover\\:transform:hover {
    transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\\:-translate-y-1:hover {
    --tw-translate-y: -0.25rem;
    transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* My Teachers & My Lessons Pages */
.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.tutors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.lesson-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.lesson-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.lesson-tutor {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.lesson-tutor .tutor-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.lesson-tutor .tutor-info h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.lesson-tutor .tutor-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6b7280;
}

.lesson-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.lesson-date,
.lesson-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4b5563;
    font-size: 0.9rem;
}

.lesson-date svg,
.lesson-time svg {
    color: #6b7280;
}

.lesson-status {
    margin-bottom: 1rem;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-approved {
    background-color: #d1fae5;
    color: #065f46;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-completed {
    background-color: #dbeafe;
    color: #1e40af;
}

.lesson-message,
.lesson-response {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #6366f1;
}

.lesson-message p,
.lesson-response p {
    margin: 0;
    font-size: 0.9rem;
    color: #4b5563;
}

/* Empty states */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state-icon {
    margin-bottom: 1.5rem;
    color: #9ca3af;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.empty-state p {
    margin: 0 0 2rem 0;
    color: #6b7280;
    max-width: 400px;
}

/* Updated tutor cards for dashboard */
.tutor-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.tutor-view-btn,
.tutor-contact-btn {
    flex: 1;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-secondary {
    background: var(--background);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.placeholder-card {
    opacity: 0.6;
    border: 2px dashed #d1d5db;
    background: #f9fafb;
}

.placeholder-actions {
    margin-top: 1rem;
}

.find-tutors-btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.9rem;
}

/* Video Preview Feature for Tutor Cards */
.tutor-card {
    position: relative;
    overflow: visible;
}

.video-preview {
    position: absolute;
    top: 0;
    right: -320px;
    width: 300px;
    height: 200px;
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--primary-color);
    z-index: 1000;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    pointer-events: none;
    overflow: hidden;
}

.video-preview.visible {
    opacity: 1;
    transform: translateX(0);
}

.preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: calc(var(--border-radius) - 2px);
}

.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface);
    border-radius: calc(var(--border-radius) - 2px);
}

.video-loading {
    flex-direction: column;
    gap: 0.5rem;
}

.video-loading p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.video-loading .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.video-placeholder {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background);
    border-radius: calc(var(--border-radius) - 2px);
    overflow: hidden;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.play-overlay:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
    width: 20px;
    height: 20px;
    margin-left: 2px;
}

.video-indicator {
    position: relative;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.video-indicator.playing {
    background: var(--success-color);
    transform: scale(1.1);
}

.video-icon {
    width: 12px;
    height: 12px;
}

.video-tooltip {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-indicator:hover .video-tooltip {
    opacity: 1;
}

/* Responsive adjustments for video preview */
@media (max-width: 1200px) {
    .video-preview {
        right: -280px;
        width: 260px;
        height: 170px;
    }
}

@media (max-width: 1024px) {
    .video-preview {
        display: none;
    }

    .video-tooltip {
        display: none;
    }
}

/* Enhanced hover effects for cards with video */
.tutor-card.hovered {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    z-index: 100;
}

.tutor-card.hovered::before {
    opacity: 1;
}

/* Ensure video preview doesn't interfere with grid layout */
.tutors-grid {
    position: relative;
    z-index: 1;
}

.tutors-grid .tutor-card:nth-child(3n) .video-preview,
.tutors-grid .tutor-card:last-child .video-preview {
    right: auto;
    left: -320px;
}

@media (max-width: 1200px) {
    .tutors-grid .tutor-card:nth-child(3n) .video-preview,
    .tutors-grid .tutor-card:last-child .video-preview {
        left: -280px;
    }
}

/* Animation keyframes */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInSlide {
    0% {
        opacity: 0;
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
        width: 0;
    }
    100% {
        opacity: 1;
        transform: translateX(0);
        width: 30%;
    }
}

/* Enhanced video preview animations */
.video-preview.visible {
    animation: fadeInSlide 0.3s ease-out;
}

/* Improved hover states for better UX */
.tutor-card:hover .video-indicator {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .video-preview,
    .video-indicator,
    .tutor-card {
        transition: none;
        animation: none;
    }

    .tutor-card:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .video-preview {
        border-width: 3px;
    }

    .video-indicator {
        border: 2px solid currentColor;
    }
}

/* Video Error and Loading States */
.video-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-align: center;
}

.video-error-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

.video-retry-btn {
    margin-top: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.video-retry-btn:hover {
    background: var(--primary-dark);
}

/* Enhanced video loading spinner */
.video-loading-enhanced {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--border-radius);
}

.video-loading-enhanced .loading-spinner {
    width: 28px;
    height: 28px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.video-loading-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Video debug info (temporary) */
.video-debug {
    font-size: 8px;
    color: #666;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 4px;
    border-radius: 2px;
    margin: 2px 0;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* TutorProfile video styles */
.video-player {
    width: 100%;
}

.tutor-video {
    width: 100%;
    max-height: 300px;
    object-fit: cover;
}

.video-poster {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
}

.close-video-btn {
    width: 100%;
    margin-top: 0.5rem;
}
